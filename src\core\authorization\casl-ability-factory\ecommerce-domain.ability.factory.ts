import { Injectable } from '@nestjs/common';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { Product } from '@core/product/entities/product.entity';
import { Order } from '@core/order/entities/order.entity';
import { Cart } from '@core/cart/entities/cart.entity';
import { CartItem } from '@core/cart_item/entities/cart_item.entity';
import { Review } from '@core/review/entities/review.entity';
import { BaseDomainAbilityFactory } from './base-domain-ability.factory';
import { DomainAbility } from './domain-ability.interface';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { ProfileType } from '@common/enumerations/profile_type.enum';

/**
 * Domain-specific ability factory for e-commerce related entities.
 *
 * This factory handles permissions for the e-commerce domain, which includes
 * Product, Order, Cart, CartItem, and Review entities. It implements business
 * rules specific to online shopping, inventory management, and customer interactions.
 *
 * Key Features:
 * - Different permission sets for STAFF vs CUSTOMER profiles
 * - Field-level restrictions for pricing and inventory data
 * - Order status management restrictions
 * - Review and rating permissions
 * - Cart and checkout process permissions
 *
 * Business Rules:
 * - STAFF profiles have full access to manage products and orders
 * - CUSTOMER profiles can only manage their own orders and reviews
 * - Pricing and inventory fields are restricted for customers
 * - Order status changes are limited based on user role
 * - Cart management follows specific business rules
 *
 * @example
 * ```typescript
 * const factory = new EcommerceDomainAbilityFactory(logger, i18n);
 * const ability = factory.createForUser(customerProfile);
 * const canCreateOrder = ability.build().can('create', 'Order');
 * ```
 */
@Injectable()
export class EcommerceDomainAbilityFactory extends BaseDomainAbilityFactory {
  /**
   * Creates a new EcommerceDomainAbilityFactory instance.
   *
   * @param logger - Logger service for debugging and monitoring
   * @param i18n - Internationalization service for error messages
   */
  constructor(logger: LoggerService, i18n: I18nService) {
    super(logger, i18n);
  }

  /**
   * Returns the domain name for e-commerce.
   *
   * @returns string - The e-commerce domain name
   */
  getDomainName(): string {
    return 'ecommerce';
  }

  /**
   * Returns the subject classes for the e-commerce domain.
   *
   * These are the entity classes that belong to the e-commerce domain:
   * - Product: Product catalog and inventory
   * - Order: Customer orders and transactions
   * - Cart: Shopping cart management
   * - CartItem: Individual cart items
   * - Review: Product reviews and ratings
   *
   * @returns any[] - Array of e-commerce domain subject classes
   */
  getSubjects(): any[] {
    return [Product, Order, Cart, CartItem, Review];
  }

  /**
   * Returns the subject types for the e-commerce domain.
   *
   * These should match the subjects returned by getSubjects() and are used
   * for CASL's subject type detection.
   *
   * @returns any[] - Array of e-commerce domain subject types
   */
  getSubjectTypes(): any[] {
    return [Product, Order, Cart, CartItem, Review];
  }

  /**
   * Applies e-commerce domain-specific permission rules.
   *
   * This method implements the business logic for e-commerce permissions:
   *
   * STAFF Profile Rules:
   * - Can manage all products (create, read, update, delete)
   * - Can manage all orders and view order details
   * - Can manage inventory and pricing
   * - Can moderate reviews and ratings
   * - Can manage cart operations
   *
   * CUSTOMER Profile Rules:
   * - Can read products and create orders
   * - Can manage their own cart and cart items
   * - Can create and update their own reviews
   * - Cannot access pricing or inventory fields
   * - Cannot modify order status
   *
   * @param profile - The user profile to apply rules for
   * @param ability - The domain ability to configure with rules
   *
   * @example
   * ```typescript
   * // STAFF can manage all products
   * if (profile.profileType === 'STAFF') {
   *   ability.can(UserActions.MANAGE, 'Product');
   *   ability.can(UserActions.UPDATE, 'Product', 'price');
   * }
   *
   * // CUSTOMER can only read products and create orders
   * if (profile.profileType === 'CUSTOMER') {
   *   ability.can(UserActions.READ, 'Product');
   *   ability.can(UserActions.CREATE, 'Order', undefined, { userId: profile.id });
   *   ability.cannot(UserActions.UPDATE, 'Product', 'price');
   * }
   * ```
   */
  protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
    this.logger.log(`Applying ecommerce domain rules for user ${profile.id} (${profile.profileType})`);

    if (profile.profileType === ProfileType.STAFF) {
      this.applyStaffRules(profile, ability);
    } else if (profile.profileType === ProfileType.CLIENT) {
      this.applyCustomerRules(profile, ability);
    }

    // Apply common restrictions
    this.applyCommonRestrictions(profile, ability);
  }

  /**
   * Applies permission rules for STAFF profiles in e-commerce.
   *
   * STAFF members have comprehensive e-commerce permissions:
   * - Full product management (CRUD operations)
   * - Order management and status updates
   * - Inventory and pricing control
   * - Review moderation
   * - Cart management oversight
   *
   * @param profile - The STAFF profile
   * @param ability - The domain ability to configure
   */
  private applyStaffRules(profile: Profile, ability: DomainAbility): void {
    // Product management
    ability.can(UserActions.MANAGE, 'Product');
    ability.can(UserActions.UPDATE, 'Product', 'price');
    ability.can(UserActions.UPDATE, 'Product', 'status');

    // Order management
    ability.can(UserActions.MANAGE, 'Order');
    ability.can(UserActions.UPDATE, 'Order', 'status');
    ability.can(UserActions.UPDATE, 'Order', 'shippingStatus');

    // Cart management
    ability.can(UserActions.MANAGE, 'Cart');
    ability.can(UserActions.MANAGE, 'CartItem');

    // Review management
    ability.can(UserActions.MANAGE, 'Review');
    ability.can(UserActions.UPDATE, 'Review', 'status');
    ability.can(UserActions.DELETE, 'Review');

    this.logger.log(`Applied STAFF ecommerce rules for user ${profile.id}`);
  }

  /**
   * Applies permission rules for CUSTOMER profiles in e-commerce.
   *
   * CUSTOMER members have limited but sufficient e-commerce permissions:
   * - Can read products and create orders
   * - Can manage their own cart and cart items
   * - Can create and update their own reviews
   * - Cannot access sensitive pricing or inventory data
   * - Cannot modify order status or other customers' data
   *
   * @param profile - The CUSTOMER profile
   * @param ability - The domain ability to configure
   */
  private applyCustomerRules(profile: Profile, ability: DomainAbility): void {
    // Product access (read-only)
    ability.can(UserActions.READ, 'Product');
    ability.cannot(UserActions.UPDATE, 'Product', 'price');
    ability.cannot(UserActions.UPDATE, 'Product', 'cost');

    // Order management (own orders only)
    ability.can(UserActions.READ, 'Order', undefined, { userId: profile.id });
    ability.can(UserActions.CREATE, 'Order', undefined, { userId: profile.id });
    ability.can(UserActions.UPDATE, 'Order', undefined, { userId: profile.id });
    ability.cannot(UserActions.UPDATE, 'Order', 'status');
    ability.cannot(UserActions.UPDATE, 'Order', 'shippingStatus');

    // Cart management (own cart only)
    ability.can(UserActions.MANAGE, 'Cart', undefined, { userId: profile.id });
    ability.can(UserActions.MANAGE, 'CartItem', undefined, { userId: profile.id });

    // Review management (own reviews only)
    ability.can(UserActions.READ, 'Review');
    ability.can(UserActions.CREATE, 'Review', undefined, { userId: profile.id });
    ability.can(UserActions.UPDATE, 'Review', undefined, { userId: profile.id });
    ability.cannot(UserActions.DELETE, 'Review');

    this.logger.log(`Applied CUSTOMER ecommerce rules for user ${profile.id}`);
  }

  /**
   * Applies common restrictions that apply to all profile types in e-commerce.
   *
   * These are global restrictions that ensure data integrity and business rules:
   * - Prevent deletion of critical entities
   * - Apply rate limiting for certain operations
   * - Ensure audit trail requirements
   * - Prevent manipulation of system fields
   *
   * @param profile - The user profile
   * @param ability - The domain ability to configure
   */
  private applyCommonRestrictions(profile: Profile, ability: DomainAbility): void {
    // Prevent deletion of critical entities
    ability.cannot(UserActions.DELETE, 'Product');
    ability.cannot(UserActions.DELETE, 'Order');

    // Prevent modification of system fields
    ability.cannot(UserActions.UPDATE, 'Product', 'id');
    ability.cannot(UserActions.UPDATE, 'Order', 'id');
    ability.cannot(UserActions.UPDATE, 'Cart', 'id');
    ability.cannot(UserActions.UPDATE, 'Review', 'id');

    // Rate limiting for review creation
    if (profile.profileType === ProfileType.CLIENT) {
      // This would typically integrate with a rate limiting service
      this.logger.log(`Rate limiting applied for customer ${profile.id}`);
    }

    this.logger.log(`Applied common ecommerce restrictions for user ${profile.id}`);
  }

  /**
   * Gets field-level restrictions for e-commerce domain entities.
   *
   * Provides fine-grained control over which fields users can access
   * based on their profile type and permissions.
   *
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns FieldRestrictions - Object mapping field names to boolean restrictions
   */
  protected getFieldRestrictions(profile: Profile, subject: string): any {
    if (subject === 'Product') {
      if (profile.profileType === ProfileType.CLIENT) {
        return {
          id: false,           // Cannot access ID
          price: false,        // Cannot access price
          cost: false,         // Cannot access cost
          inventory: false,    // Cannot access inventory
          status: false,       // Cannot access status
          name: true,          // Can access name
          description: true,   // Can access description
          category: true,      // Can access category
          images: true         // Can access images
        };
      }
    }

    if (subject === 'Order') {
      if (profile.profileType === ProfileType.CLIENT) {
        return {
          id: false,           // Cannot access ID
          status: false,       // Cannot access status
          shippingStatus: false, // Cannot access shipping status
          total: true,         // Can access total
          items: true,         // Can access items
          shippingAddress: true, // Can access shipping address
          billingAddress: true   // Can access billing address
        };
      }
    }

    return {};
  }

  /**
   * Gets conditions for e-commerce domain entities.
   *
   * Provides conditions that restrict access based on user ownership
   * and business rules.
   *
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns AbilityConditions - Object containing conditions for permission checks
   */
  protected getConditions(profile: Profile, subject: string): any {
    if (subject === 'Order' && profile.profileType === ProfileType.CLIENT) {
      return {
        userId: profile.id  // Can only access own orders
      };
    }

    if (subject === 'Cart' && profile.profileType === ProfileType.CLIENT) {
      return {
        userId: profile.id  // Can only access own cart
      };
    }

    if (subject === 'Review' && profile.profileType === ProfileType.CLIENT) {
      return {
        userId: profile.id  // Can only access own reviews
      };
    }

    return {};
  }
}
