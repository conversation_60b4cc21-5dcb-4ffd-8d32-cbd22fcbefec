import { Controller } from '@nestjs/common';
import { StoreService } from './store.service';
import { LoggerService } from '@common/logger/logger.service';

@Controller({
  path: 'store',
  version: '1',
})
export class StoreController {
  constructor(
    private readonly storeService: StoreService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(StoreController.name);
  }
}
