import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreatePermission1739650579977 implements MigrationInterface {

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Permission Table
    await queryRunner.createTable(
      new Table({
        name: 'permission',
        columns: [
          { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
          { name: 'tenant_id', type: 'bigint', isNullable: true },
          { name: 'status', type: 'varchar' },
          { name: 'created_at', type: 'timestamp', default: 'now()' },
          { name: 'created_by', type: 'varchar' },
          { name: 'updated_at', type: 'timestamp', default: 'now()' },
          { name: 'updated_by', type: 'varchar', isNullable: true },
          { name: 'deleted_at', type: 'timestamp', isNullable: true },
          {
            name: 'name',
            type: 'varchar',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'varchar',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'permission',
      new TableIndex({
        name: 'IDX_PERMISSION',
        columnNames: [
          'id',
          'name',
          'description',
          'status',
          'created_at',
          'created_by',
          'updated_at',
          'updated_by',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('permission', 'IDX_PERMISSION');
    await queryRunner.dropTable('permission');
  }

}
