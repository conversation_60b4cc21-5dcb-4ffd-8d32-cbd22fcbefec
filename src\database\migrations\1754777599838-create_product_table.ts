import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateProductTable1754777599838 implements MigrationInterface {

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'product',
        columns: [
          { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
          { name: 'tenant_id', type: 'bigint', isNullable: true },
          { name: 'status', type: 'varchar' },
          { name: 'created_at', type: 'timestamp', default: 'now()' },
          { name: 'created_by', type: 'varchar' },
          { name: 'updated_at', type: 'timestamp', default: 'now()' },
          { name: 'updated_by', type: 'varchar', isNullable: true },
          { name: 'deleted_at', type: 'timestamp', isNullable: true },
          { name: 'name', type: 'varchar' },
          { name: 'description', type: 'varchar', isNullable: true },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'product',
      new TableIndex({
        name: 'IDX_PRODUCT_FIELDS',
        columnNames: [
          'id',
          'status',
          'created_at',
          'updated_at',
          'name',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('product', 'IDX_PRODUCT_FIELDS');
    await queryRunner.dropTable('product');
  }

}
