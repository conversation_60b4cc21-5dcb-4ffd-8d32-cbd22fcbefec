import { AutoMap } from '@automapper/classes';
import { ProfileDto } from '@core/profile/dto/profile.dto';
import { WalletDetails } from '@common/types/index.type';
import { EntityDto } from '@common/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { WalletDto } from '@core/customer/dto/wallet.dto';

export class CustomerDto extends EntityDto {
  @AutoMap()
  @ApiProperty({
    description: 'Unique identifier for the customer',
    example: '123A45Q',
    type: String,
    name: 'referrerCode',
  })
  referrerCode: string;

  @AutoMap(() => ProfileDto)
  @ApiProperty({
    description: 'Profile information of the customer',
    type: ProfileDto,
    name: 'profile',
  })
  profile: ProfileDto;

  @AutoMap(() => WalletDto)
  wallet: WalletDetails;
}
