import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateStaffProfilePk1754555026074 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add a foreign key to the profile table.
        await queryRunner.createForeignKey(
          'staff',
          new TableForeignKey({
              columnNames: ['profile_id'],
              referencedColumnNames: ['id'],
              referencedTableName: 'profile',
              onDelete: 'CASCADE',
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const staffTable = await queryRunner.getTable('staff');
        const fk = staffTable.foreignKeys.find(
          (fk) => fk.columnNames.indexOf('profile_id') !== -1,
        );
        await queryRunner.dropForeignKey('staff', fk);
    }

}
