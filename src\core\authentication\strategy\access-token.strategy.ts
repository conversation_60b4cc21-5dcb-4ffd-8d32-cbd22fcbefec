import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { JwtPayload } from 'jsonwebtoken';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileService } from '@core/profile/profile.service';

interface TenantAwareJwtPayload extends JwtPayload {
  tenantId?: number;
  isGlobalUser?: boolean;
  type?: string;
}

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private readonly configService: ConfigService,
    private readonly profileService: ProfileService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
    });
  }

  async validate(payload: TenantAwareJwtPayload): Promise<Profile> {
    const { email, tenantId, isGlobalUser, type } = payload;
    
    // Validate token type
    if (type !== 'access') {
      throw new Error('Invalid token type');
    }

    const profile = await this.profileService.findProfileByEmail(email);
    await this.profileService.checkProfileEligibility(profile);

    // Validate tenant access for non-global users
    if (!isGlobalUser && tenantId && profile.tenantId && profile.tenantId !== tenantId) {
      throw new Error('User does not have access to this tenant');
    }

    // Add tenant context to profile for downstream use
    (profile as any).currentTenantId = tenantId;
    (profile as any).isGlobalUser = isGlobalUser;

    return profile;
  }
}
