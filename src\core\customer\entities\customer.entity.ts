import { AutoMap } from '@automapper/classes';
import { Profile } from '@core/profile/entities/profile.entity';
import { Review } from '@core/review/entities/review.entity';
import { Column, Entity, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { Order } from '@core/order/entities/order.entity';
import { AbstractEntity } from '@common/base.entity';
import { ReferralDetails, WalletDetails } from '@common/types/index.type';


/**
 * Customer entity representing a customer in the system.
 * It contains a reference to the customer's profile, wallet, and their orders, reviews, and carts.
 */
@Entity({ name: 'customer' })
export class Customer extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'referral', nullable: true, type: 'jsonb'})
  referral: ReferralDetails;

  @OneToOne(() => Profile, { cascade: true, eager: true })
  @JoinColumn({ name: 'profile_id', referencedColumnName: 'id' })
  profile: Profile;

  @AutoMap()
  @Column({ name: 'wallet_details', type: 'jsonb', nullable: true })
  wallet: WalletDetails;

  @OneToMany(() => Review, (review) => review.customer, { eager: true })
  reviews: Review[]; // Assuming this is a one-to-many relationship with Review

  @OneToMany(() => Order, (order) => order.customer)
  orders: Order[];
}
