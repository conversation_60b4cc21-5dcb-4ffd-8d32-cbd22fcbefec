# Event Module Documentation

## Overview

The Event Module provides a comprehensive system for handling entity lifecycle events and automatic audit field management. It ensures that `createdBy`, `createdAt`, `updatedBy`, `updatedAt`, and `deletedAt` fields are automatically populated for all entities that extend `AbstractEntity`.

## Architecture

### Components

1. **AbstractEntitySubscriber** - TypeORM subscriber that handles entity lifecycle events
2. **ProfileContextInterceptor** - NestJS interceptor that sets user context
3. **EventsModule** - Module configuration and exports

### Flow Diagram

```
Request → ProfileContextInterceptor → AbstractEntitySubscriber → Database
   ↓              ↓                        ↓
Set User    Extract User Info    Set Audit Fields
Context     from Request         (createdBy, updatedBy, etc.)
```

## Components Details

### 1. AbstractEntitySubscriber

**Location**: `src/events/subscriber/abstract-entity-subscriber.ts`

**Purpose**: Automatically manages audit fields for all entities extending `AbstractEntity`.

**Key Features**:
- **Error Handling**: Robust error handling that doesn't block database operations
- **Logging**: Comprehensive logging for debugging and monitoring
- **Null Safety**: Handles cases where event.entity might be null/undefined
- **Audit Trail**: Maintains complete audit trail for all entity operations

**Lifecycle Methods**:

#### beforeInsert()
- Sets `createdBy`, `updatedBy`, and `createdAt` fields
- Logs the operation with entity name and user information
- Handles errors gracefully without blocking the insert operation

#### beforeUpdate()
- Sets `updatedBy` and `updatedAt` fields
- Validates that event.entity exists before processing
- Logs the operation for audit purposes

#### beforeRemove()
- Sets `updatedBy` and `deletedAt` fields for soft deletes
- Maintains audit trail even for deleted entities

#### afterInsert(), afterUpdate(), afterRemove()
- Logs successful operations for monitoring
- Provides entity ID and name for tracking

### 2. ProfileContextInterceptor

**Location**: `src/profile-context/profile-context.interceptor.ts`

**Purpose**: Extracts user information from requests and sets the current user context.

**Key Features**:
- **Flexible User Extraction**: Handles various user profile formats
- **Fallback Mechanisms**: Provides sensible defaults when user info is missing
- **Error Resilience**: Continues operation even if user extraction fails
- **Comprehensive Logging**: Tracks user context changes

**User Context Logic**:
1. Extracts user profile from request
2. Builds user name from available fields (firstName, lastName, email)
3. Falls back to `User-{ID}` format if no name fields available
4. Uses 'SYSTEM' as ultimate fallback
5. Sets context using `AbstractEntity.setCurrentUser()`

### 3. EventsModule

**Location**: `src/events/events.module.ts`

**Purpose**: Configures and exports event-related services.

**Current Services**:
- `NotificationListenerService` - Handles email notifications
- Future: Can be extended with additional event listeners

## Configuration

### Database Configuration

The `AbstractEntitySubscriber` is registered in the TypeORM configuration:

```typescript
// src/config/database.ts
export const dbConfig = (): PostgresConnectionOptions => ({
  // ... other config
  subscribers: [AbstractEntitySubscriber],
  // ... other config
});
```

### Global Interceptor Registration

The `ProfileContextInterceptor` is registered globally in the main application:

```typescript
// src/app.module.ts
{
  provide: APP_INTERCEPTOR,
  useClass: ProfileContextInterceptor,
}
```

## Usage Examples

### Entity Creation

When you create an entity that extends `AbstractEntity`:

```typescript
// The audit fields are automatically set
const newProduct = new Product();
newProduct.name = 'Sample Product';
newProduct.price = 100;

// When saved, these fields are automatically populated:
// - createdBy: "John Doe" (from ProfileContextInterceptor)
// - createdAt: "2024-01-15T10:30:00Z"
// - updatedBy: "John Doe"
// - updatedAt: "2024-01-15T10:30:00Z"
await productRepository.save(newProduct);
```

### Entity Updates

When you update an entity:

```typescript
// Audit fields are automatically updated
const product = await productRepository.findOne(1);
product.price = 150;

// When saved, updatedBy and updatedAt are automatically set:
// - updatedBy: "Jane Smith" (current user)
// - updatedAt: "2024-01-15T11:45:00Z"
await productRepository.save(product);
```

### Entity Deletion

When you soft-delete an entity:

```typescript
// Audit fields are automatically set for soft deletes
const product = await productRepository.findOne(1);

// When soft-deleted:
// - updatedBy: "Admin User" (current user)
// - deletedAt: "2024-01-15T12:00:00Z"
await productRepository.softRemove(product);
```

## Audit Field Structure

All entities extending `AbstractEntity` automatically have these audit fields:

```typescript
export abstract class AbstractEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  @Column({ name: 'updated_by' })
  updatedBy: string;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;
}
```

## Logging and Monitoring

### Log Levels

- **INFO**: Normal operations (user context setting, audit field updates)
- **WARN**: Missing user profile, null entity events
- **ERROR**: Exceptions during processing (logged but don't block operations)

### Sample Log Output

```
[ProfileContextInterceptor] Setting user context - User: John Doe, ID: 123
[AbstractEntitySubscriber] Setting audit fields for Product - createdBy: John Doe, createdAt: 2024-01-15T10:30:00Z
[AbstractEntitySubscriber] Successfully created Product with ID: 456
```

## Error Handling

### Graceful Degradation

The system is designed to continue operating even when errors occur:

1. **Missing User Context**: Falls back to 'SYSTEM' user
2. **Subscriber Errors**: Logged but don't block database operations
3. **Null Entities**: Handled gracefully with appropriate warnings

### Error Recovery

- Database operations continue even if audit field setting fails
- User context errors don't prevent request processing
- Comprehensive error logging for debugging

## Best Practices

### 1. Entity Design

```typescript
// Always extend AbstractEntity for automatic audit fields
@Entity('products')
export class Product extends AbstractEntity {
  @Column()
  name: string;
  
  @Column('decimal', { precision: 10, scale: 2 })
  price: number;
}
```

### 2. User Context

```typescript
// Ensure your authentication middleware sets request.user
// The ProfileContextInterceptor will automatically extract it
```

### 3. Testing

```typescript
// In tests, you can manually set user context
AbstractEntity.setCurrentUser('Test User');

// Or test with different user contexts
AbstractEntity.setCurrentUser('Admin User');
```

## Troubleshooting

### Common Issues

1. **Audit fields not being set**
   - Check if entity extends `AbstractEntity`
   - Verify `AbstractEntitySubscriber` is registered in database config
   - Ensure `ProfileContextInterceptor` is registered globally

2. **User context not available**
   - Verify authentication middleware sets `request.user`
   - Check if `ProfileContextInterceptor` is properly configured
   - Review logs for user context warnings

3. **Missing logs**
   - Ensure `LoggerService` is properly configured
   - Check log level settings

### Debug Mode

Enable detailed logging by setting log level to DEBUG:

```typescript
// In your logger configuration
{
  level: 'debug'
}
```

## Future Enhancements

### Potential Improvements

1. **Audit Trail Service**: Dedicated service for querying audit history
2. **Custom Audit Fields**: Support for domain-specific audit fields
3. **Audit Export**: Export audit trails for compliance
4. **Real-time Notifications**: Notify administrators of critical changes
5. **Audit Analytics**: Dashboard for audit field analytics

### Extension Points

The modular design allows for easy extension:

```typescript
// Custom subscriber for specific entities
@EventSubscriber()
export class CustomEntitySubscriber implements EntitySubscriberInterface<CustomEntity> {
  // Custom audit logic
}
```

## Security Considerations

1. **User Context Validation**: Ensure user context is properly validated
2. **Audit Field Protection**: Prevent direct modification of audit fields
3. **Logging Security**: Avoid logging sensitive information in audit logs
4. **Access Control**: Implement proper access controls for audit data

## Performance Considerations

1. **Minimal Overhead**: Audit field setting is lightweight
2. **Async Operations**: After-event logging doesn't block database operations
3. **Selective Logging**: Only log essential information to avoid performance impact
4. **Database Indexes**: Consider indexing audit fields for query performance 