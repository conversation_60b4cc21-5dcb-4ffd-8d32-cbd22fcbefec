import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class AddressInfoDto {
  @AutoMap(() => String)
  @ApiProperty({
    description: 'The latitude of the address',
    example: '37.7749',
  })
  latitude: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The longitude of the address',
    example: '-122.4194',
  })
  longitude: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The house address of the user',
    example: '123 Main St',
  })
  houseAddress: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The state of the user',
    example: 'California',
    required: false,
  })
  state?: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The city of the user',
    example: 'San Francisco',
  })
  city: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The state of the user',
    example: 'California',
  })
  postalCode?: string;
}
