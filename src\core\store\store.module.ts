import { Modu<PERSON> } from '@nestjs/common';
import { StoreService } from './store.service';
import { StoreController } from './store.controller';
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Store } from '@core/store/entities/store.entity';
import { StoreValidationService } from './store.validation.service';
import { StoreMapperService } from './store.mapper.service';

@Module({
  controllers: [StoreController],
  providers: [StoreService, StoreValidationService, StoreMapperService],
  imports: [LoggerModule, TypeOrmModule.forFeature([Store])], // Add your entities here
  exports: [StoreService]
})
export class StoreModule {}
