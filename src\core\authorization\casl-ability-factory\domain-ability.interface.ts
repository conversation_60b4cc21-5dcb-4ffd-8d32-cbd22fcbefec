import { PureAbility } from '@casl/ability';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';

/**
 * Represents a domain-specific ability wrapper that provides a unified interface
 * for defining CASL permissions within a specific business domain.
 * 
 * This interface abstracts the CASL AbilityBuilder API and provides methods
 * for defining permissions (can/cannot) and building the final ability.
 * 
 * @example
 * ```typescript
 * const domainAbility: DomainAbility = {
 *   can: (action, subject, field, conditions) => {
 *     // Define permission logic
 *     return true;
 *   },
 *   cannot: (action, subject, field, conditions) => {
 *     // Define restriction logic
 *     return true;
 *   },
 *   build: () => PureAbility // Return the built CASL ability
 * };
 * ```
 */
export interface DomainAbility {
  /**
   * Defines a permission for a specific action, subject, and optional field/conditions.
   * 
   * @param action - The user action to permit (e.g., CREATE, READ, UPDATE, DELETE)
   * @param subject - The subject/entity to apply the permission to
   * @param field - Optional field restriction (e.g., 'price', 'status')
   * @param conditions - Optional conditions that must be met for the permission
   * @returns boolean - Always returns true to indicate the permission was defined successfully
   * 
   * @example
   * ```typescript
   * // Allow users to read their own products
   * ability.can(UserActions.READ, 'Product', undefined, { userId: user.id });
   * 
   * // Allow staff to update product prices
   * ability.can(UserActions.UPDATE, 'Product', 'price');
   * ```
   */
  can(action: UserActions, subject: any, field?: string, conditions?: any): boolean;

  /**
   * Defines a restriction for a specific action, subject, and optional field/conditions.
   * 
   * @param action - The user action to restrict (e.g., CREATE, READ, UPDATE, DELETE)
   * @param subject - The subject/entity to apply the restriction to
   * @param field - Optional field restriction (e.g., 'price', 'status')
   * @param conditions - Optional conditions that must be met for the restriction
   * @returns boolean - Always returns true to indicate the restriction was defined successfully
   * 
   * @example
   * ```typescript
   * // Prevent customers from deleting products
   * ability.cannot(UserActions.DELETE, 'Product');
   * 
   * // Prevent updating critical fields
   * ability.cannot(UserActions.UPDATE, 'Product', 'id');
   * ```
   */
  cannot(action: UserActions, subject: any, field?: string, conditions?: any): boolean;

  /**
   * Builds and returns the final CASL PureAbility instance.
   * 
   * This method should configure the ability with proper subject type detection
   * and return a fully configured PureAbility that can be used for permission checks.
   * 
   * @returns PureAbility - The built CASL ability instance
   * 
   * @example
   * ```typescript
   * const ability = domainAbility.build();
   * const canRead = ability.can('read', product);
   * ```
   */
  build(): PureAbility;
}

/**
 * Factory interface for creating domain-specific abilities.
 * 
 * Each business domain should implement this interface to provide
 * domain-specific permission logic. This enables modular and
 * maintainable authorization code.
 * 
 * @example
 * ```typescript
 * class EcommerceDomainFactory implements DomainAbilityFactory {
 *   createForUser(profile: Profile): DomainAbility {
 *     // Return domain-specific ability
 *   }
 *   
 *   getDomainName(): string {
 *     return 'ecommerce';
 *   }
 * }
 * ```
 */
export interface DomainAbilityFactory {
  /**
   * Creates a domain-specific ability for a given user profile.
   * 
   * This method should analyze the user's profile (including roles and permissions)
   * and return a DomainAbility that defines all permissions for this domain.
   * 
   * @param profile - The user profile containing roles and permissions
   * @returns DomainAbility - The domain-specific ability for the user
   * 
   * @example
   * ```typescript
   * const factory = new EcommerceDomainFactory();
   * const ability = factory.createForUser(userProfile);
   * const canCreateProduct = ability.build().can('create', 'Product');
   * ```
   */
  createForUser(profile: Profile): DomainAbility;

  /**
   * Returns the name of the domain this factory handles.
   * 
   * This should be a unique identifier for the domain (e.g., 'ecommerce', 'authorization').
   * Used for registration and lookup in the unified ability factory.
   * 
   * @returns string - The domain name
   * 
   * @example
   * ```typescript
   * const domainName = factory.getDomainName(); // 'ecommerce'
   * ```
   */
  getDomainName(): string;
}

/**
 * Represents conditions that can be applied to CASL permissions.
 * 
 * Conditions are used to restrict permissions based on specific criteria,
 * such as user ownership, status, or other business rules.
 * 
 * @example
 * ```typescript
 * const conditions: AbilityConditions = {
 *   userId: user.id,           // User can only access their own resources
 *   status: 'active',          // Only active resources
 *   role: 'admin',            // Role-based conditions
 *   department: 'sales'        // Department-specific conditions
 * };
 * ```
 */
export interface AbilityConditions {
  [key: string]: any;
}

/**
 * Represents field-level restrictions for CASL permissions.
 * 
 * Field restrictions allow fine-grained control over which fields
 * a user can access or modify within an entity.
 * 
 * @example
 * ```typescript
 * const fieldRestrictions: FieldRestrictions = {
 *   price: false,      // Cannot access price field
 *   status: true,      // Can access status field
 *   description: true, // Can access description field
 *   id: false          // Cannot access id field
 * };
 * ```
 */
export interface FieldRestrictions {
  [field: string]: boolean;
} 