import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Cart } from '@core/cart/entities/cart.entity';
import { Item } from '@core/item/entities/item.entity';
import { AbstractEntity } from '@common/base.entity';

@Entity({ name: 'cart_item' })
export class CartItem extends AbstractEntity{

  @ManyToOne(() => Cart, (cart) => cart.items, { eager: false })
  @JoinColumn({ name: 'cart_id', referencedColumnName: 'id' })
  cart: Cart;

  @OneToOne(() => Item, { eager: true })
  @JoinColumn({ name: 'item_id', referencedColumnName: 'id' })
  item: Item;

  @AutoMap()
  @Column({name: 'quantity', type: 'int'})
  quantity: number;
}
