import { AbstractEntity } from '@common/base.entity';
import { <PERSON><PERSON><PERSON>ler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { Observable } from 'rxjs';

@Injectable()
export class ProfileContextInterceptor implements NestInterceptor {
  private readonly logger = new LoggerService(ProfileContextInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    try {
      const request = context.switchToHttp().getRequest();
      const profile = request.user;
      
      let userName = 'SYSTEM';
      let userId = 'SYSTEM';
      let tenantId: number | undefined;
      let isGlobalUser = false;

      // Extract user information from the request
      if (profile) {
        // Try to get user ID first
        if ((profile as any).id) {
          userId = (profile as any).id.toString();
        }

        // Extract tenant context from profile or request headers
        tenantId = (profile as any).currentTenantId || 
                   (profile as any).tenantId || 
                   request.headers['x-tenant-id'] ? 
                   parseInt(request.headers['x-tenant-id']) : undefined;
        
        isGlobalUser = (profile as any).isGlobalUser || 
                      request.headers['x-global-user'] === 'true';

        // Build user name from available fields
        if (profile.firstName && profile.lastName) {
          userName = `${profile.firstName} ${profile.lastName}`;
        } else if (profile.firstName) {
          userName = profile.firstName;
        } else if (profile.lastName) {
          userName = profile.lastName;
        } else if (profile.email) {
          userName = profile.email;
        } else {
          userName = `User-${userId}`;
        }

        this.logger.log(`Setting user context - User: ${userName}, ID: ${userId}, Tenant: ${tenantId || 'global'}, Global: ${isGlobalUser}`);
      } else {
        // Extract tenant context from request headers for unauthenticated requests
        tenantId = request.headers['x-tenant-id'] ? 
                   parseInt(request.headers['x-tenant-id']) : undefined;
        isGlobalUser = request.headers['x-global-user'] === 'true';
        
        this.logger.warn('No user profile found in request, using SYSTEM context');
      }

      // Set the current user context for all entities
      AbstractEntity.setCurrentUser(userName);

      // Store tenant context in request for downstream use
      request.tenantId = tenantId;
      request.isGlobalUser = isGlobalUser;

      return next.handle();
    } catch (error) {
      this.logger.error(`Error in ProfileContextInterceptor: ${error.message}`, error.stack);
      
      // Fallback to SYSTEM context if there's an error
      AbstractEntity.setCurrentUser('SYSTEM');
      
      return next.handle();
    }
  }
}
