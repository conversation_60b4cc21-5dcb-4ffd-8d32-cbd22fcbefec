import { AutoMap } from '@automapper/classes';
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEmail, IsEnum, IsOptional, IsPhoneNumber, IsString } from 'class-validator';
import { AddressDetails } from '@common/types/index.type';
import { AddressInfoDto } from '@core/profile/dto/address-info.dto';

export class CreateProfileDto {
  @AutoMap(() => String)
  @ApiProperty({
    description: 'The first name of the user',
    example: 'John',
    required: true,
    type: String,
  })
  @IsString()
  firstName: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The last name of the user',
    example: 'Doe',
    required: true,
    type: String,
  })
  @IsString()
  lastName?: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
    required: true,
    type: String,
  })
  @IsEmail()
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The phone number of the user',
    example: '+2348123456789',
    required: true,
    type: String,
  })
  @IsPhoneNumber()
  phoneNumber: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The password of the user',
    example: 'SecureP@ssw0rd',
    required: true,
    type: String,
  })
  @IsString()
  password: string;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The gender of the user',
    enum: Gender,
    required: false,
    type: String
  })
  @IsEnum(Gender)
  gender: Gender;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'The profile type of the user',
    enum: ProfileType,
    required: true,
    type: String,
  })
  @IsEnum(ProfileType)
  profileType: ProfileType;

  @AutoMap(() => AddressInfoDto)
  @ApiProperty({
    description: 'The address of the user',
    type: AddressInfoDto,
    required: false,
  })
  @Type(() => AddressInfoDto)
  @IsOptional()
  address: AddressDetails;
}
