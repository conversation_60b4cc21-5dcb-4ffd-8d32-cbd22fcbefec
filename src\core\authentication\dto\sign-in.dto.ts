import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class SignInDto {
  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The email of the user',
    name: 'email',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The password of the user',
    name: 'password',
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
