import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '../../../common/logger/logger.service';
import { EntityServiceStrategy } from '../../../common/entity.service.strategy';
import { Role } from './entities/role.entity';
import { EntityStatus } from '../../../common/base.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoleValidationService } from './role.validation.service';
import { DatabaseAction } from '../../../common/enumerations/db_action.enum';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class RoleService implements EntityServiceStrategy<Role> {
  constructor(
    private readonly loggerService: LoggerService,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
    private readonly roleValidator: RoleValidationService,
    private readonly i18n: I18nService,
  ) {
    this.loggerService.setContext(RoleService.name);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const role: Role = await this.roleRepo.findOne({
          where: { id: id },
        });
        role.status = EntityStatus.ACTIVE;
        await this.roleRepo.save(role);
      }),
    );
  }

  async create(data: Role): Promise<Role> {
    await this.roleValidator.validate(data, DatabaseAction.CREATE);
    return Promise.resolve(await this.roleRepo.save(data));
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const role: Role = await this.roleRepo.findOne({
          where: { id: id },
        });
        role.status = EntityStatus.INACTIVE;
        await this.roleRepo.save(role);
      }),
    );
  }

  async findByPk(id: number): Promise<Role | null> {
    return Promise.resolve(await this.roleRepo.findOne({ where: { id: id } }));
  }

  async modify(id: number, data: Role): Promise<Role> {
    await this.roleValidator.validate(data, DatabaseAction.UPDATE);

    const role = await this.findByPk(id);

    if (!role) {
      throw new NotFoundException(this.i18n.t('errors.not_found', {args: { entity: "Role" }}));
    }

    return Promise.resolve(await this.roleRepo.save(data));
  }
}
