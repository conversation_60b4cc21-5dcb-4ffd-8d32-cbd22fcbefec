import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateProductItemFk1754777634315 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add a foreign key to the profile table.
        await queryRunner.createForeignKey(
          'item',
          new TableForeignKey({
              columnNames: ['product_id'],
              referencedColumnNames: ['id'],
              referencedTableName: 'product',
              onDelete: 'CASCADE',
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const itemTable = await queryRunner.getTable('item');
        const fk = itemTable.foreignKeys.find(
          (fk) => fk.columnNames.indexOf('product_id') !== -1,
        );
        await queryRunner.dropForeignKey('item', fk);
    }

}
