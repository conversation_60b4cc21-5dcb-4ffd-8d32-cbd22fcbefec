import { Modu<PERSON> } from "@nestjs/common";
import { LoggerModule } from '@common/logger/logger.module';
import { NotificationModule } from '@core/notification/notification.module';
import { NotificationListenerService } from "./listener/notification.listener.service";
import { AbstractEntitySubscriber } from "./subscriber/abstract-entity-subscriber";

@Module({
  providers: [NotificationListenerService, AbstractEntitySubscriber],
  exports: [NotificationListenerService, AbstractEntitySubscriber],
  imports: [LoggerModule, NotificationModule],
})
export class EventsModule {}
