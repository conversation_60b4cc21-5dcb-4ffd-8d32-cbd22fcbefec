import { Repository } from 'typeorm';

/**
 * Base repository class that extends TypeORM's Repository.
 * It provides a method to set the context for tenant-based queries.
 */
export class BaseRepository<T> extends Repository<T> {
  private tenantId: number;
  private isGlobalUser = false;

  setContext(tenantId: number, isGlobalUser: boolean) {
    this.tenantId = tenantId;
    this.isGlobalUser = isGlobalUser;
  }

  async findWithContext(where: any = {}) {
    if (!this.isGlobalUser && this.tenantId) {
      where.tenantId = this.tenantId;
    }
    return this.find({ where });
  }
}
