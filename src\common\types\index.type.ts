/**
 * This file contains various TypeScript type definitions used across the application.
 */
export type AuthDetails = {
  password?: string;
  passwordResetDate?: Date;
  failedLoginCount?: number;
  refreshToken?: string;
  lastLogin?: Date;
  lastGoogleSignIn?: Date;
}

export type SettingsDetails = {
  [key: string]: any;
}

export type PictureDetails = {
  url?: string;
  publicId?: string;
}

export type WalletDetails = {
  balance: number;
  currency: string;
  paystackCustomerId: string;
}

export type VerificationDetails = {
  verified?: boolean; // Indicates if the verification is complete
  kycVerified?: boolean; // Indicates if KYC verification is complete
  verificationDate?: Date; // Date when the verification was completed
}

export type AddressDetails = {
  latitude: string;
  longitude: string;
  houseAddress: string;
  state?: string;
  city: string;
  postalCode?: string;
}

export type JwtPayload = {
  email: string;
  sub: number;
};

export type JwtPayloadWithRt = JwtPayload & { refreshToken: string };

export type ReferralDetails = {
  code: string; // Unique code for the referral
}

export type PaginationQueryParams = {
  page: number;
  limit: number;
  search: string;
  filter: string;
};
