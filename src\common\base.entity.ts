import { AutoMap } from '@automapper/classes';
import {
  BaseEntity,
  BeforeInsert,
  BeforeSoftRemove,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum EntityStatus {
  ACTIVE = 'A',
  INACTIVE = 'I',
}

export abstract class AbstractEntity extends BaseEntity {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'tenant_id', type: 'bigint', nullable: true })
  tenantId?: number;

  @AutoMap(() => String)
  @Column({
    name: 'status',
    type: 'enum',
    enum: EntityStatus,
    default: EntityStatus.INACTIVE,
  })
  status: string;

  @AutoMap()
  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @AutoMap()
  @Column({ name: 'created_by' })
  createdBy: string;

  @AutoMap()
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  @AutoMap()
  @Column({ name: 'updated_by' })
  updatedBy: string;

  @AutoMap()
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Static user context setter
  private static currentUser: string = 'SYSTEM';

  // Static method to set the current user
  static setCurrentUser(user: string) {
    this.currentUser = user;
  }

  // Getter for current user (can be used in lifecycle hooks)
  static getCurrentUser(): string {
    return this.currentUser;
  }

  @BeforeInsert()
  preInsert() {
    this.status = this.status ?? EntityStatus.ACTIVE;
    this.createdBy = AbstractEntity.getCurrentUser();  // Use static method to get user
    this.createdAt = new Date();
  }

  @BeforeUpdate()
  preUpdate() {
    this.updatedBy = AbstractEntity.getCurrentUser();  // Use static method to get user
    this.updatedAt = new Date();
  }

  @BeforeSoftRemove()
  preDelete() {
    this.deletedAt = new Date();
    this.updatedBy = AbstractEntity.getCurrentUser();  // Use static method to get user
  }
}
