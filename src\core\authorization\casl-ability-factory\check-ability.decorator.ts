import { SetMetadata } from '@nestjs/common';
import { UserActions } from '@common/enumerations/user_actions.enum';

/**
 * Represents a single ability check requirement.
 * 
 * This interface defines the structure for a permission check, including
 * the action to perform, the subject to check against, and optional
 * field and condition restrictions.
 * 
 * @example
 * ```typescript
 * const requirement: AbilityCheck = {
 *   action: UserActions.READ,
 *   subject: 'Product',
 *   field: 'price',
 *   conditions: { userId: user.id }
 * };
 * ```
 */
export interface AbilityCheck {
  /** The user action to check (e.g., CREATE, READ, UPDATE, DELETE) */
  action: UserActions;
  /** The subject/entity to check against (e.g., 'Product', 'Order') */
  subject: any;
  /** Optional field restriction (e.g., 'price', 'status') */
  field?: string;
  /** Optional conditions that must be met (e.g., { userId: user.id }) */
  conditions?: any;
}

/**
 * Metadata key for storing ability check requirements.
 * 
 * This constant is used internally by the ability guard to retrieve
 * the ability requirements from route metadata.
 */
export const CHECK_ABILITY_KEY = 'check_ability';

/**
 * Decorator for defining ability requirements on controller methods.
 * 
 * This decorator allows you to declaratively define permission requirements
 * for controller methods. The ability guard will automatically check these
 * requirements before allowing the request to proceed.
 * 
 * @param requirements - Array of ability check requirements
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckAbility(
 *   { action: UserActions.READ, subject: 'Product' },
 *   { action: UserActions.UPDATE, subject: 'Product', field: 'price' }
 * )
 * async updateProduct() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckAbility = (...requirements: AbilityCheck[]) =>
  SetMetadata(CHECK_ABILITY_KEY, requirements);

/**
 * Convenience decorator for CREATE action requirements.
 * 
 * This decorator simplifies the common case of checking CREATE permissions
 * for a specific subject and optional field.
 * 
 * @param subject - The subject/entity to check against
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckCreate('Product')
 * async createProduct() {
 *   // Method implementation
 * }
 * 
 * @CheckCreate('Product', 'price')
 * async createProductWithPrice() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckCreate = (subject: any, field?: string) =>
  CheckAbility({ action: UserActions.CREATE, subject, field });

/**
 * Convenience decorator for READ action requirements.
 * 
 * This decorator simplifies the common case of checking READ permissions
 * for a specific subject and optional field.
 * 
 * @param subject - The subject/entity to check against
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckRead('Product')
 * async getProducts() {
 *   // Method implementation
 * }
 * 
 * @CheckRead('Product', 'price')
 * async getProductPrice() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckRead = (subject: any, field?: string) =>
  CheckAbility({ action: UserActions.READ, subject, field });

/**
 * Convenience decorator for UPDATE action requirements.
 * 
 * This decorator simplifies the common case of checking UPDATE permissions
 * for a specific subject and optional field.
 * 
 * @param subject - The subject/entity to check against
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckUpdate('Product')
 * async updateProduct() {
 *   // Method implementation
 * }
 * 
 * @CheckUpdate('Product', 'price')
 * async updateProductPrice() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckUpdate = (subject: any, field?: string) =>
  CheckAbility({ action: UserActions.UPDATE, subject, field });

/**
 * Convenience decorator for DELETE action requirements.
 * 
 * This decorator simplifies the common case of checking DELETE permissions
 * for a specific subject and optional field.
 * 
 * @param subject - The subject/entity to check against
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckDelete('Product')
 * async deleteProduct() {
 *   // Method implementation
 * }
 * 
 * @CheckDelete('Product', 'inventory')
 * async deleteProductInventory() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckDelete = (subject: any, field?: string) =>
  CheckAbility({ action: UserActions.DELETE, subject, field });

/**
 * Convenience decorator for MANAGE action requirements.
 * 
 * This decorator simplifies the common case of checking MANAGE permissions
 * for a specific subject and optional field. MANAGE typically includes
 * all CRUD operations.
 * 
 * @param subject - The subject/entity to check against
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckManage('Product')
 * async manageProducts() {
 *   // Method implementation
 * }
 * 
 * @CheckManage('Product', 'pricing')
 * async manageProductPricing() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckManage = (subject: any, field?: string) =>
  CheckAbility({ action: UserActions.MANAGE, subject, field });

/**
 * Domain-specific decorator for Product entity permissions.
 * 
 * This decorator provides a convenient way to check permissions specifically
 * for the Product entity, which is part of the e-commerce domain.
 * 
 * @param action - The user action to check
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckProductAbility(UserActions.READ)
 * async getProducts() {
 *   // Method implementation
 * }
 * 
 * @CheckProductAbility(UserActions.UPDATE, 'price')
 * async updateProductPrice() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckProductAbility = (action: UserActions, field?: string) =>
  CheckAbility({ action, subject: 'Product', field });

/**
 * Domain-specific decorator for Order entity permissions.
 * 
 * This decorator provides a convenient way to check permissions specifically
 * for the Order entity, which is part of the e-commerce domain.
 * 
 * @param action - The user action to check
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckOrderAbility(UserActions.CREATE)
 * async createOrder() {
 *   // Method implementation
 * }
 * 
 * @CheckOrderAbility(UserActions.UPDATE, 'status')
 * async updateOrderStatus() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckOrderAbility = (action: UserActions, field?: string) =>
  CheckAbility({ action, subject: 'Order', field });

/**
 * Domain-specific decorator for Profile entity permissions.
 * 
 * This decorator provides a convenient way to check permissions specifically
 * for the Profile entity, which is part of the authorization domain.
 * 
 * @param action - The user action to check
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckProfileAbility(UserActions.READ)
 * async getProfiles() {
 *   // Method implementation
 * }
 * 
 * @CheckProfileAbility(UserActions.UPDATE, 'email')
 * async updateProfileEmail() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckProfileAbility = (action: UserActions, field?: string) =>
  CheckAbility({ action, subject: 'Profile', field });

/**
 * Domain-specific decorator for Role entity permissions.
 * 
 * This decorator provides a convenient way to check permissions specifically
 * for the Role entity, which is part of the authorization domain.
 * 
 * @param action - The user action to check
 * @param field - Optional field restriction
 * @returns MethodDecorator - The decorator function
 * 
 * @example
 * ```typescript
 * @CheckRoleAbility(UserActions.MANAGE)
 * async manageRoles() {
 *   // Method implementation
 * }
 * 
 * @CheckRoleAbility(UserActions.UPDATE, 'permissions')
 * async updateRolePermissions() {
 *   // Method implementation
 * }
 * ```
 */
export const CheckRoleAbility = (action: UserActions, field?: string) =>
  CheckAbility({ action, subject: 'Role', field }); 