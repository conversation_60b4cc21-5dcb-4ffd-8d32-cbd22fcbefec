import { Modu<PERSON> } from "@nestjs/common";
import { EntitiesSeederService } from "./entities-seeder.service";
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from "@nestjs/typeorm";
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { Role } from '@core/authorization/role/entities/role.entity';
import { Profile } from '@core/profile/entities/profile.entity';
import { Tenant } from '@core/tenant/entities/tenant.entity';


@Module({
  providers: [EntitiesSeederService],
  exports: [EntitiesSeederService],
  imports: [
    LoggerModule,
    TypeOrmModule.forFeature([Profile, Role, Permission, Tenant]),
  ]
})
export class EntitiesSeederModule {}
