import { LoggerModule } from '@common/logger/logger.module';
import { CustomerModule } from "@core/customer/customer.module";
import { StaffModule } from "@core/staff/staff.module";
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Profile } from "./entities/profile.entity";
import { ProfileController } from "./profile.controller";
import { ProfileMapperService } from './profile.mapper.service';
import { ProfileService } from "./profile.service";
import { ProfileValidationService } from './profile.validation.service';

@Module({
  controllers: [ProfileController],
  providers: [ProfileService, ProfileValidationService, ProfileMapperService],
  exports: [ProfileService, ProfileValidationService],
  imports: [TypeOrmModule.forFeature([Profile]), LoggerModule, StaffModule, CustomerModule],
})
export class ProfileModule {}
