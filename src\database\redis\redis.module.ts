import { Module } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { RedisService } from './redis.service';
import { RedisController } from './redis.controller';
import { LoggerModule } from '@common/logger/logger.module';
import KeyvRedis, { Keyv } from '@keyv/redis';
import { CacheableMemory } from 'cacheable';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    LoggerModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        const redisConfig = configService.get('redis');
        
        // Use Redis if configured, otherwise fall back to in-memory
        if (redisConfig?.uri) {
          return {
            ttl: 60 * 1000,
            refreshThreshold: 1200,
            store: new KeyvRedis(redisConfig.uri),
          };
        } else {
          // Fallback to in-memory cache if Redis is not configured
          return {
            ttl: 60 * 1000,
            refreshThreshold: 1200,
            store: new Keyv({
              store: new CacheableMemory({ ttl: 60000, lruSize: 5000 }),
            }),
          };
        }
      },
    }),
  ],
  controllers: [RedisController],
  providers: [
    RedisService,
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: CacheInterceptor,
    // },
  ],
  exports: [RedisService],
})
export class RedisModule {}
