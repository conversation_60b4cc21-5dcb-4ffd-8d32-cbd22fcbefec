import { Modu<PERSON> } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { TenantController } from './tenant.controller';
import { TenantMapperService } from './tenant.mapper.service';
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from '@core/tenant/entities/tenant.entity';
import { TenantValidationService } from './tenant.validation.service';

@Module({
  controllers: [TenantController],
  exports: [TenantService],
  imports: [LoggerModule, TypeOrmModule.forFeature([Tenant])], // Add Tenant entity here when created
  providers: [TenantService, TenantMapperService, TenantValidationService],
})
export class TenantModule {}
