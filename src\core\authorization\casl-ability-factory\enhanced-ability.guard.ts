import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CaslAbilityFactoryService } from './casl-ability-factory.service';
import { CHECK_ABILITY_KEY, AbilityCheck } from './check-ability.decorator';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { LoggerService } from '@common/logger/logger.service';

/**
 * Enhanced NestJS guard for checking CASL abilities.
 *
 * This guard automatically checks user permissions based on decorators applied
 * to controller methods. It integrates with the CASL ability system to provide
 * comprehensive permission checking across all domains.
 *
 * Key Features:
 * - Automatic permission checking based on decorators
 * - Support for field-level permissions
 * - Condition-based permission checking
 * - Comprehensive error handling and logging
 * - Integration with unified ability system
 *
 * Usage:
 * The guard works in conjunction with ability decorators to automatically
 * check permissions before allowing route execution.
 *
 * @example
 * ```typescript
 * @Controller('products')
 * @UseGuards(EnhancedAbilityGuard)
 * export class ProductController {
 *   @Get()
 *   @CheckRead('Product')
 *   async getProducts() {
 *     // Method will only execute if user has READ permission on Product
 *   }
 *
 *   @Put(':id')
 *   @CheckUpdate('Product', 'price')
 *   async updateProduct() {
 *     // Method will only execute if user has UPDATE permission on Product price field
 *   }
 * }
 * ```
 */
@Injectable()
export class EnhancedAbilityGuard implements CanActivate {
  /**
   * Creates a new EnhancedAbilityGuard instance.
   *
   * @param reflector - NestJS reflector for accessing metadata
   * @param caslAbilityFactory - CASL ability factory service
   * @param logger - Logger service for debugging and monitoring
   */
  constructor(
    private readonly reflector: Reflector,
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(EnhancedAbilityGuard.name);
  }

  /**
   * Determines if the current request can proceed based on user permissions.
   *
   * This method is called by NestJS before executing the route handler.
   * It checks if the user has the required permissions based on the
   * ability decorators applied to the method.
   *
   * Process:
   * 1. Retrieves ability requirements from method metadata
   * 2. Gets the user from the request context
   * 3. Creates a unified ability for the user
   * 4. Checks each requirement against the user's ability
   * 5. Throws ForbiddenException if any check fails
   *
   * @param context - NestJS execution context containing request information
   * @returns Promise<boolean> - True if the request can proceed, false otherwise
   * @throws ForbiddenException - If the user lacks required permissions
   *
   * @example
   * ```typescript
   * // This method is called automatically by NestJS
   * const canProceed = await guard.canActivate(context);
   * if (!canProceed) {
   *   throw new ForbiddenException('Access denied');
   * }
   * ```
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requirements = this.reflector.getAllAndOverride<AbilityCheck[]>(
      CHECK_ABILITY_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requirements) {
      this.logger.log('No ability requirements found, allowing access');
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      this.logger.warn('No user found in request context');
      throw new ForbiddenException('User not authenticated');
    }

    const ability = this.caslAbilityFactory.createForUser(user);

    // Check each requirement
    for (const requirement of requirements) {
      const { action, subject, field, conditions } = requirement;

      try {
        const canPerform = field
          ? ability.can(action, subject, field)
          : ability.can(action, subject, conditions);

        if (!canPerform) {
          this.logger.warn(
            `Access denied: User ${(user as any).id} cannot ${action} ${subject}${field ? ` field: ${field}` : ''}`,
          );
          throw new ForbiddenException(
            `You are not allowed to ${action} ${subject}${field ? ` field: ${field}` : ''}`,
          );
        }
      } catch (error) {
        if (error instanceof ForbiddenException) {
          throw error;
        }
        this.logger.error(`Error checking ability: ${error.message}`, error.stack);
        throw new ForbiddenException('Permission check failed');
      }
    }

    this.logger.log(`Access granted for user ${(user as any).id} to ${requirements.length} requirements`);
    return true;
  }
}

/**
 * Domain-specific guard for Product entity permissions.
 *
 * This guard extends the EnhancedAbilityGuard to provide product-specific
 * functionality. It can be used to add product-specific logic or validation
 * before permission checking.
 *
 * @example
 * ```typescript
 * @Controller('products')
 * @UseGuards(ProductAbilityGuard)
 * export class ProductController {
 *   @Get()
 *   @CheckRead('Product')
 *   async getProducts() {
 *     // Product-specific logic can be added here
 *   }
 * }
 * ```
 */
@Injectable()
export class ProductAbilityGuard extends EnhancedAbilityGuard {
  /**
   * Determines if the current request can proceed for product operations.
   *
   * This method can be overridden to add product-specific validation
   * or logic before performing permission checks.
   *
   * @param context - NestJS execution context
   * @returns Promise<boolean> - True if the request can proceed
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Add product-specific logic here if needed
    console.log('Product ability guard activated');
    return super.canActivate(context);
  }
}

/**
 * Domain-specific guard for Order entity permissions.
 *
 * This guard extends the EnhancedAbilityGuard to provide order-specific
 * functionality. It can be used to add order-specific logic or validation
 * before permission checking.
 *
 * @example
 * ```typescript
 * @Controller('orders')
 * @UseGuards(OrderAbilityGuard)
 * export class OrderController {
 *   @Get()
 *   @CheckRead('Order')
 *   async getOrders() {
 *     // Order-specific logic can be added here
 *   }
 * }
 * ```
 */
@Injectable()
export class OrderAbilityGuard extends EnhancedAbilityGuard {
  /**
   * Determines if the current request can proceed for order operations.
   *
   * This method can be overridden to add order-specific validation
   * or logic before performing permission checks.
   *
   * @param context - NestJS execution context
   * @returns Promise<boolean> - True if the request can proceed
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Add order-specific logic here if needed
    console.log('Order ability guard activated');
    return super.canActivate(context);
  }
}

/**
 * Domain-specific guard for Profile entity permissions.
 *
 * This guard extends the EnhancedAbilityGuard to provide profile-specific
 * functionality. It can be used to add profile-specific logic or validation
 * before permission checking.
 *
 * @example
 * ```typescript
 * @Controller('profiles')
 * @UseGuards(ProfileAbilityGuard)
 * export class ProfileController {
 *   @Get()
 *   @CheckRead('Profile')
 *   async getProfiles() {
 *     // Profile-specific logic can be added here
 *   }
 * }
 * ```
 */
@Injectable()
export class ProfileAbilityGuard extends EnhancedAbilityGuard {
  /**
   * Determines if the current request can proceed for profile operations.
   *
   * This method can be overridden to add profile-specific validation
   * or logic before performing permission checks.
   *
   * @param context - NestJS execution context
   * @returns Promise<boolean> - True if the request can proceed
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Add profile-specific logic here if needed
    console.log('Profile ability guard activated');
    return super.canActivate(context);
  }
}


