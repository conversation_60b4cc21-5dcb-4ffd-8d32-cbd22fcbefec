import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, DefaultValuePipe, Get, Param, ParseIntPipe, Patch, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateStaffDto } from './dto/activate-staff.dto';
import { DeactivateStaffDto } from './dto/deactivate-staff.dto';
import { StaffDto } from './dto/staff.dto';
import { Staff } from './entities/staff.entity';
import { StaffService } from './staff.service';

@ApiTags('Staff Endpoints')
@Controller({
  path: 'staff',
  version: '1',
})
export class StaffController {
  constructor(
    private readonly staffService: StaffService,
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(StaffController.name);
  }
  @ApiOperation({ summary: 'Get all staff' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @Get()
  async getAllStaff(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const { items, meta, links } = await this.staffService.findAllStaff({ page, limit, search, filter }, route);
      const staff = await this.classMapper.mapArrayAsync(items, Staff, StaffDto);
      const data = new Pagination(staff, meta, links)
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Staff' } }), data };
    });
  }

  @ApiOperation({ summary: 'Get one staff' })
  @Get(':id')
  async getOneStaff(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const staff = await this.staffService.findOneStaff(id);
      const data = await this.classMapper.mapAsync(staff, Staff, StaffDto);
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Staff' } }), data };
    });
  }

  @ApiOperation({ summary: 'Activate staff' })
  @ApiBody({ type: ActivateStaffDto })
  @Patch('activate')
  async activateCustomers(@Body() body: ActivateStaffDto) {
    return CoreUtils.handleRequest(async () => {
      await this.staffService.activate(body.ids);
      return { message: this.i18n.t('success.activated', { args: { entity: 'Staff' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate staff' })
  @ApiBody({ type: DeactivateStaffDto })
  @Patch('deactivate')
  async deactivateCustomers(@Body() body: DeactivateStaffDto) {
    return CoreUtils.handleRequest(async () => {
      await this.staffService.deactivate(body.ids);
      return { message: this.i18n.t('success.deactivated', { args: { entity: 'Staff' } }), data: null };
    });
  }
}
