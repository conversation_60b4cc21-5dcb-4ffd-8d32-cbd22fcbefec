import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { StaffDto } from './dto/staff.dto';
import { Staff } from './entities/staff.entity';

@Injectable()
export class StaffMapperService extends AutomapperProfile {
  constructor(
    @InjectMapper() mapper: Mapper,
    private readonly logger: LoggerService,
  ) {
    super(mapper);
    this.logger.setContext(StaffMapperService.name);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, StaffDto, Staff);
      createMap(mapper, Staff, StaffDto);
    };
  }
}
