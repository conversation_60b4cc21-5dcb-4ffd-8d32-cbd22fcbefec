import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateTenantTable1754552841511 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
          new Table({
              name: 'tenant',
              columns: [
                  { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
                  { name: 'tenant_id', type: 'bigint', isNullable: true },
                  { name: 'status', type: 'varchar' },
                  { name: 'created_at', type: 'timestamp', default: 'now()' },
                  { name: 'created_by', type: 'varchar' },
                  { name: 'updated_at', type: 'timestamp', default: 'now()' },
                  { name: 'updated_by', type: 'varchar', isNullable: true },
                  { name: 'deleted_at', type: 'timestamp', isNullable: true },
                  {name: 'name', type: 'varchar',  isUnique: true, length: '50'},
                  {name: 'code', type: 'varchar',  isUnique: true, length: '20'},
                  {name: 'price_factor', type: 'decimal', precision: 10, scale: 2, default: 1.00},
                  {name: 'currency_code', type: 'varchar', length: '3'},
                  {name: 'currency_symbol', type: 'varchar', length: '3'},
              ]
          }),
          true
        )

        await queryRunner.createIndex(
          'tenant',
          new TableIndex({
              name: 'IDX_TENANT_FIELDS',
              columnNames: [
                  'id',
                  'status',
                  'created_at',
                  'updated_at',
                  'name',
              ],
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropIndex('tenant', 'IDX_TENANT_FIELDS');
        await queryRunner.dropTable('tenant');
    }

}
