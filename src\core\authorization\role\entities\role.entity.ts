import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>, ManyToMany, Unique } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { AbstractEntity } from '@common/base.entity';


@Entity({ name: 'role' })
@Unique(['name'])
export class Role extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap()
  @Column({ name: 'description' })
  description: string;

  @ManyToMany(() => Permission, {
    eager: true,
  })
  @JoinTable({
    name: 'role_permission',
    joinColumn: { name: 'role_id' },
    inverseJoinColumn: { name: 'permission_id' },
  })
  permissions: Array<Permission>;
}
