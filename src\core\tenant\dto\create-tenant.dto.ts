import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsPositive, Length } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class CreateTenantDto {
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  name: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  code: string;

  @AutoMap()
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @ApiProperty()
  priceFactor: number;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @Length(3, 3)
  @ApiProperty()
  currencyCode: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @Length(1, 3)
  @ApiProperty()
  currencySymbol: string;
}
