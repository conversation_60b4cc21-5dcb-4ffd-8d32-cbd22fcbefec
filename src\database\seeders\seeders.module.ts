import { Module } from '@nestjs/common';
import { EntitiesSeederModule } from '@database/seeders/entities-seeder/entities-seeder.module';
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import configuration from '../../config';
import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { SeederService } from './seeder.service';

@Module({
  imports: [
    EntitiesSeederModule,
    LoggerModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...configService.get('database'),
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
  ],
  providers: [SeederService],
  exports: [SeederService],
})
export class SeedersModule {}
