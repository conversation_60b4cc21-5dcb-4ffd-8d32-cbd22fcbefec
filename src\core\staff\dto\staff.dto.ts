import { AutoMap } from "@automapper/classes";
import { ProfileDto } from "@core/profile/dto/profile.dto";
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/base.dto';

export class StaffDto extends EntityDto {
  @AutoMap(() => ProfileDto)
  @ApiProperty({
    type: ProfileDto,
    description: 'The profile associated with the staff member',
    name: 'profile',
  })
  profile: ProfileDto
}
