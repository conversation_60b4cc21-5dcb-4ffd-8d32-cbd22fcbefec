import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { Tenant } from './entities/tenant.entity';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class TenantValidationService
  implements ValidationStrategy<Tenant>
{
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Tenant)
    private readonly tenantRepo: Repository<Tenant>,
  ) {
    this.logger.setContext(TenantValidationService.name);
  }

  async validate(data: Tenant, action: DatabaseAction) {
    const existingTenant = await this.tenantRepo.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE === action && existingTenant) {
      throw new ConflictException(
        this.i18n.t('errors.tenant_already_exists', {
          args: { entity: 'Tenant' },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingTenant) {
      throw new NotFoundException(
        this.i18n.t('errors.tenant_not_found'),
      );
    }
  }
}
