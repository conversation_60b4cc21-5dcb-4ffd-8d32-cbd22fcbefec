import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { ProfileDto } from '@core/profile/dto/profile.dto';
import { Profile } from '@core/profile/entities/profile.entity';
import { Injectable } from '@nestjs/common';
import { CreateProfileDto } from './dto/create-profile.dto';

@Injectable()
export class ProfileMapperService extends AutomapperProfile {
  constructor(
    @InjectMapper() mapper: Mapper,
    private readonly logger: LoggerService,
  ) {
    super(mapper);
    this.logger.setContext(ProfileMapperService.name);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Profile, ProfileDto);
      createMap(mapper, ProfileDto, Profile);
      createMap(mapper, CreateProfileDto, Profile);
    };
  }
}
