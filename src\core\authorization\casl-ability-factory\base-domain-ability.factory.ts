import { Injectable } from '@nestjs/common';
import { AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects, PureAbility } from '@casl/ability';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { DomainAbility, DomainAbilityFactory, AbilityConditions, FieldRestrictions } from './domain-ability.interface';

/**
 * Abstract base class for creating domain-specific CASL ability factories.
 * 
 * This class provides a common foundation for all domain-specific ability factories,
 * including shared functionality for permission checking, field restrictions, and
 * conditions. It implements the DomainAbilityFactory interface and provides
 * a template method pattern for domain-specific rule application.
 * 
 * Key Features:
 * - Abstract methods for domain-specific configuration
 * - Built-in permission checking utilities
 * - Field restriction and condition helpers
 * - CASL AbilityBuilder integration
 * - Logging and internationalization support
 * 
 * @example
 * ```typescript
 * class EcommerceDomainFactory extends BaseDomainAbilityFactory {
 *   getDomainName(): string {
 *     return 'ecommerce';
 *   }
 *   
 *   getSubjects(): any[] {
 *     return [Product, Order, Cart];
 *   }
 *   
 *   protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
 *     // Define ecommerce-specific rules
 *     if (profile.profileType === 'STAFF') {
 *       ability.can(UserActions.MANAGE, 'Product');
 *     }
 *   }
 * }
 * ```
 */
export abstract class BaseDomainAbilityFactory implements DomainAbilityFactory {
  protected readonly logger: LoggerService;
  protected readonly i18n: I18nService;

  /**
   * Creates a new BaseDomainAbilityFactory instance.
   * 
   * @param logger - Logger service for debugging and monitoring
   * @param i18n - Internationalization service for error messages
   */
  constructor(logger: LoggerService, i18n: I18nService) {
    this.logger = logger;
    this.i18n = i18n;
    this.logger.setContext(this.constructor.name);
  }

  /**
   * Returns the name of the domain this factory handles.
   * 
   * This should be a unique identifier for the domain (e.g., 'ecommerce', 'authorization').
   * Used for registration and lookup in the unified ability factory.
   * 
   * @returns string - The domain name
   * 
   * @example
   * ```typescript
   * class EcommerceDomainFactory extends BaseDomainAbilityFactory {
   *   getDomainName(): string {
   *     return 'ecommerce';
   *   }
   * }
   * ```
   */
  abstract getDomainName(): string;

  /**
   * Returns the subject classes/entities that this domain handles.
   * 
   * These are used for type inference and subject type detection in CASL.
   * Should return an array of entity classes that belong to this domain.
   * 
   * @returns any[] - Array of subject classes
   * 
   * @example
   * ```typescript
   * getSubjects(): any[] {
   *   return [Product, Order, Cart, CartItem, Review];
   * }
   * ```
   */
  abstract getSubjects(): any[];

  /**
   * Returns the subject types for this domain.
   * 
   * These are used for CASL's subject type detection and should match
   * the subjects returned by getSubjects().
   * 
   * @returns any[] - Array of subject types
   * 
   * @example
   * ```typescript
   * getSubjectTypes(): any[] {
   *   return [Product, Order, Cart, CartItem, Review];
   * }
   * ```
   */
  abstract getSubjectTypes(): any[];

  /**
   * Creates a domain-specific ability for a given user profile.
   * 
   * This method implements the template method pattern:
   * 1. Gets domain subjects and types
   * 2. Creates a CASL AbilityBuilder
   * 3. Wraps it in a DomainAbility interface
   * 4. Applies domain-specific rules via applyDomainRules()
   * 5. Returns the configured ability
   * 
   * @param profile - The user profile containing roles and permissions
   * @returns DomainAbility - The domain-specific ability for the user
   * 
   * @example
   * ```typescript
   * const factory = new EcommerceDomainFactory(logger, i18n);
   * const ability = factory.createForUser(userProfile);
   * const canCreateProduct = ability.build().can('create', 'Product');
   * ```
   */
  createForUser(profile: Profile): DomainAbility {
    const subjects = this.getSubjects();
    const subjectTypes = this.getSubjectTypes();
    
    type DomainSubjects = InferSubjects<typeof subjects[number]> | 'all';
    type DomainAbilityType = PureAbility<[UserActions, DomainSubjects]>;

    const builder = new AbilityBuilder<DomainAbilityType>(
      PureAbility as AbilityClass<DomainAbilityType>,
    );

    // Create a wrapper that provides the domain-specific ability
    const domainAbility: DomainAbility = {
      can: (action: UserActions, subject: any, field?: string, conditions?: AbilityConditions): boolean => {
        if (field) {
          builder.can(action, subject, field, conditions);
        } else {
          builder.can(action, subject, conditions);
        }
        return true; // CASL builder methods return void, so we return true to indicate success
      },
      cannot: (action: UserActions, subject: any, field?: string, conditions?: AbilityConditions): boolean => {
        if (field) {
          builder.cannot(action, subject, field, conditions);
        } else {
          builder.cannot(action, subject, conditions);
        }
        return true; // CASL builder methods return void, so we return true to indicate success
      },
      build: () => builder.build({
        detectSubjectType: (item) =>
          item.constructor as ExtractSubjectType<DomainSubjects>,
      }),
    };

    // Apply domain-specific rules
    this.applyDomainRules(profile, domainAbility);

    return domainAbility;
  }

  /**
   * Abstract method for applying domain-specific permission rules.
   * 
   * This method should be implemented by subclasses to define the specific
   * permission logic for their domain. It receives the user profile and
   * a DomainAbility instance to configure.
   * 
   * @param profile - The user profile containing roles and permissions
   * @param ability - The domain ability to configure with rules
   * 
   * @example
   * ```typescript
   * protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
   *   // Staff can manage all products
   *   if (profile.profileType === 'STAFF') {
   *     ability.can(UserActions.MANAGE, 'Product');
   *   }
   *   
   *   // Customers can only read and create their own products
   *   if (profile.profileType === 'CUSTOMER') {
   *     ability.can(UserActions.READ, 'Product', undefined, { userId: profile.id });
   *     ability.cannot(UserActions.DELETE, 'Product');
   *   }
   * }
   * ```
   */
  protected abstract applyDomainRules(profile: Profile, ability: DomainAbility): void;

  /**
   * Checks if a user has a specific permission.
   * 
   * This method examines the user's role and its associated permissions
   * to determine if they have the specified permission.
   * 
   * @param profile - The user profile to check
   * @param permissionName - The name of the permission to check for
   * @returns boolean - True if the user has the permission, false otherwise
   * 
   * @example
   * ```typescript
   * if (this.hasPermission(profile, 'product.create')) {
   *   ability.can(UserActions.CREATE, 'Product');
   * }
   * ```
   */
  protected hasPermission(profile: Profile, permissionName: string): boolean {
    if (!profile.role?.permissions) return false;
    return profile.role.permissions.some(permission => permission.name === permissionName);
  }

  /**
   * Checks if a user has any of the specified permissions.
   * 
   * This method is useful for checking multiple alternative permissions
   * where having any one of them is sufficient.
   * 
   * @param profile - The user profile to check
   * @param permissionNames - Array of permission names to check for
   * @returns boolean - True if the user has at least one of the permissions, false otherwise
   * 
   * @example
   * ```typescript
   * if (this.hasAnyPermission(profile, ['product.create', 'product.manage'])) {
   *   ability.can(UserActions.CREATE, 'Product');
   * }
   * ```
   */
  protected hasAnyPermission(profile: Profile, permissionNames: string[]): boolean {
    if (!profile.role?.permissions) return false;
    return profile.role.permissions.some(permission => permissionNames.includes(permission.name));
  }

  /**
   * Checks if a user has all of the specified permissions.
   * 
   * This method is useful for checking multiple required permissions
   * where having all of them is necessary.
   * 
   * @param profile - The user profile to check
   * @param permissionNames - Array of permission names to check for
   * @returns boolean - True if the user has all of the permissions, false otherwise
   * 
   * @example
   * ```typescript
   * if (this.hasAllPermissions(profile, ['product.read', 'product.update'])) {
   *   ability.can(UserActions.UPDATE, 'Product');
   * }
   * ```
   */
  protected hasAllPermissions(profile: Profile, permissionNames: string[]): boolean {
    if (!profile.role?.permissions) return false;
    return permissionNames.every(permissionName => 
      profile.role.permissions.some(permission => permission.name === permissionName)
    );
  }

  /**
   * Gets field-level restrictions for a specific subject and user.
   * 
   * This method can be overridden by subclasses to provide domain-specific
   * field restrictions. By default, it returns an empty object (no restrictions).
   * 
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns FieldRestrictions - Object mapping field names to boolean restrictions
   * 
   * @example
   * ```typescript
   * protected getFieldRestrictions(profile: Profile, subject: string): FieldRestrictions {
   *   if (subject === 'Product' && profile.profileType === 'CUSTOMER') {
   *     return {
   *       price: false,      // Cannot access price
   *       cost: false,       // Cannot access cost
   *       description: true, // Can access description
   *       name: true         // Can access name
   *     };
   *   }
   *   return {};
   * }
   * ```
   */
  protected getFieldRestrictions(profile: Profile, subject: string): FieldRestrictions {
    // Override in subclasses to provide field-level restrictions
    return {};
  }

  /**
   * Gets conditions for a specific subject and user.
   * 
   * This method can be overridden by subclasses to provide domain-specific
   * conditions. By default, it returns an empty object (no conditions).
   * 
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns AbilityConditions - Object containing conditions for permission checks
   * 
   * @example
   * ```typescript
   * protected getConditions(profile: Profile, subject: string): AbilityConditions {
   *   if (subject === 'Product' && profile.profileType === 'CUSTOMER') {
   *     return {
   *       userId: profile.id,  // Only own products
   *       status: 'active'     // Only active products
   *     };
   *   }
   *   return {};
   * }
   * ```
   */
  protected getConditions(profile: Profile, subject: string): AbilityConditions {
    // Override in subclasses to provide conditions
    return {};
  }
} 