import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, Get, Param, ParseIntPipe, Patch, Post, Put } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { ActivateTenantDto } from './dto/activate-tenant.dto';
import { DeactivateTenantDto } from './dto/deactivate-tenant.dto';
import { CreateTenantDto } from './dto/create-tenant.dto';
import { UpdateTenantDto } from './dto/update-tenant.dto';
import { TenantDto } from './dto/tenant-dto';
import { Tenant } from './entities/tenant.entity';
import { TenantService } from './tenant.service';

@ApiTags('Tenant Endpoints')
@Controller({
  path: 'tenant',
  version: '1',
})
export class TenantController {
  constructor(
    private readonly tenantService: TenantService,
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(TenantController.name);
  }

  @ApiOperation({ summary: 'Get one tenant' })
  @Get(':id')
  async getOneTenant(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      const tenant = await this.tenantService.findByPk(id);
      const data = await this.classMapper.mapAsync(tenant, Tenant, TenantDto);
      return {
        message: this.i18n.t('success.retrieved', { args: { entity: 'Tenant' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Create tenant' })
  @ApiBody({ type: CreateTenantDto })
  @Post()
  async createTenant(@Body() body: CreateTenantDto) {
    return CoreUtils.handleRequest(async () => {
      const tenant = await this.tenantService.create(body as any);
      const data = await this.classMapper.mapAsync(tenant, Tenant, TenantDto);
      return {
        message: this.i18n.t('success.created', { args: { entity: 'Tenant' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Update tenant' })
  @ApiBody({ type: UpdateTenantDto })
  @Put(':id')
  async updateTenant(@Param('id', ParseIntPipe) id: number, @Body() body: UpdateTenantDto) {
    return CoreUtils.handleRequest(async () => {
      const tenant = await this.tenantService.modify(id, body as any);
      const data = await this.classMapper.mapAsync(tenant, Tenant, TenantDto);
      return {
        message: this.i18n.t('success.updated', { args: { entity: 'Tenant' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Activate tenants' })
  @ApiBody({ type: ActivateTenantDto })
  @Patch('activate')
  async activateTenants(@Body() body: ActivateTenantDto) {
    return CoreUtils.handleRequest(async () => {
      await this.tenantService.activate(body.ids);
      return {
        message: this.i18n.t('success.activated', { args: { entity: 'Tenant' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate tenants' })
  @ApiBody({ type: DeactivateTenantDto })
  @Patch('deactivate')
  async deactivateTenants(@Body() body: DeactivateTenantDto) {
    return CoreUtils.handleRequest(async () => {
      await this.tenantService.deactivate(body.ids);
      return {
        message: this.i18n.t('success.deactivated', { args: { entity: 'Tenant' } }),
        data: null,
      };
    });
  }
}
