import { AutoMap } from '@automapper/classes';
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { Role } from '@core/authorization/role/entities/role.entity';
import { Column, Entity, JoinColumn, OneToOne, Unique } from 'typeorm';
import { AbstractEntity } from '@common/base.entity';
import { AddressDetails, AuthDetails, SettingsDetails, VerificationDetails } from '@common/types/index.type';


@Entity({ name: 'profile' })
@Unique(['email', 'phoneNumber'])
export class Profile extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'first_name', type: 'varchar' })
  firstName: string;

  @AutoMap()
  @Column({ name: 'last_name', type: 'varchar', nullable: true })
  lastName: string;

  @AutoMap()
  @Column({ name: 'email', type: 'varchar', unique: true })
  email: string;

  @AutoMap()
  @Column({ name: 'phone_number', type: 'varchar', unique: true })
  phoneNumber: string; // Phone number format should start with country code, e.g., +2347012345678

  @AutoMap(() => String)
  @Column({
    name: 'gender',
    type: 'varchar',
    enum: Gender,
    default: Gender.DEFAULT,
  })
  gender: Gender;

  @AutoMap(() => String)
  @Column({
    name: 'profile_status',
    type: 'enum',
    enum: ProfileStatus,
    default: ProfileStatus.DEACTIVATED,
  })
  profileStatus: ProfileStatus; // Set profile status to INACTIVE if the status of is INACTIVE.

  @AutoMap(() => String)
  @Column({
    name: 'profile_type',
    type: 'enum',
    enum: ProfileType,
    default: ProfileType.NOT_SPECIFIED,
  })
  profileType: ProfileType;

  @AutoMap()
  @Column({ name: 'auth_info', type: 'jsonb', nullable: true })
  authInfo: AuthDetails;

  @AutoMap()
  @Column({ name: 'address', type: 'jsonb', nullable: true })
  address: AddressDetails;

  @AutoMap()
  @Column({ name: 'verification_info', type: 'jsonb', nullable: true })
  verificationInfo: VerificationDetails;

  @OneToOne(() => Role, { cascade: true, eager: true })
  @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })
  role?: Role; // If profileType is CUSTOMER, then the role should be defaulted to CUSTOMER_ROLE.

  @AutoMap()
  @Column({ name: 'settings', type: 'jsonb', nullable: true })
  settings?: SettingsDetails;

  //coupon - rate(%), code, expiry date, etc.
}
