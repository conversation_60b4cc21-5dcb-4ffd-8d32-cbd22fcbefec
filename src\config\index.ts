import { config } from 'dotenv';
import { dbConfig } from './database';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { IMailerConfig, mailerConfig } from '@config/mail';
import { IOAuthConfig, oauthConfig } from '@config/oauth';
import { IRedisConfig, redisConfig } from '@config/redis';
import { cloudinaryConfig, gcsConfig, IStorageOptions, s3Config } from '@config/storage';
import { IOnesignalConfig, onesignalConfig } from '@config/one-signal';
import { IPremblyConfig, premblyConfig } from '@config/prembly';



config();

interface iConfig {
  baseUrl: string;
  database: PostgresConnectionOptions;
  env: string;
  keys: {
    secret: string;
    // termiiApiKey: string;
    // termiiBaseUrl: string;
    // termiiConfigurationId: string;
    stripeApiKey?: string;
  };
  mails: IMailerConfig;
  oauth: IOAuthConfig;
  port: number;
  redis: IRedisConfig;
  storage: IStorageOptions;
  onesignal: IOnesignalConfig;
  prembly: IPremblyConfig;
}

export default (): Partial<iConfig> => ({
  baseUrl: process.env.APP_BASE_URL || 'http://localhost',
  database: dbConfig(),
  env: process.env.NODE_ENV || 'development',
  keys: {
    secret: process.env.SECRET,
    // termiiApiKey: process.env.TERMII_API_KEY,
    // termiiBaseUrl: process.env.TERMII_BASE_URL,
    // termiiConfigurationId: process.env.TERMII_CONFIGURATION_ID,
    stripeApiKey: process.env.STRIPE_API_KEY,
  },
  mails: mailerConfig(),
  oauth: oauthConfig(),
  port: parseInt(process.env.PORT, 10) || 3080,
  redis: redisConfig(),
  storage: {
    s3: s3Config(),
    gcs: gcsConfig(),
    cloudinary: cloudinaryConfig(),
  },
  onesignal: onesignalConfig(),
  prembly: premblyConfig(),
});
