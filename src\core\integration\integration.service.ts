import { Injectable } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import Stripe from 'stripe';

@Injectable()
export class IntegrationService {
  private readonly oneSignalAppId: string;
  private readonly oneSignalApiKey: string;

  private readonly premblyApiKey: string;
  private readonly premblyAppId: string;

  // private readonly termiiApiKey: string;
  // private readonly termiiBaseUrl: string;
  // private readonly termiiConfigId: string;

  private readonly stripeApiKey: string;

  private readonly stripe: Stripe;

  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    ){
    this.logger.setContext(IntegrationService.name)

    // if (!this.oneSignalAppId || !this.oneSignalApiKey) {
    //   this.logger.error(`Configuration not found.`);
    //   throw new NotFoundException('OneSignal configuration not found.');
    // }

    // this.premblyAppId = this.configService.get<string>('prembly.appId')
    // this.premblyApiKey = this.configService.get<string>('prembly.apiKey')
    //
    // if (!this.premblyAppId || !this.premblyApiKey) {
    //   this.logger.error(`Configuration not found.`);
    //   throw new NotFoundException('Prembly configuration not found.');
    // }

    // this.termiiApiKey = this.configService.get<string>('keys.termiiApiKey');
    // this.termiiBaseUrl = this.configService.get<string>('keys.termiiBaseUrl');
    // this.termiiConfigId = this.configService.get<string>('keys.termiiConfigurationId');
    this.stripeApiKey = this.configService.get<string>('keys.stripeApiKey');

    // Only initialize Stripe if API key is provided
    if (this.stripeApiKey) {
      this.stripe = new Stripe(this.stripeApiKey, {
        apiVersion: null,
      });
    } else {
      this.logger.warn('Stripe API key not provided. Stripe functionality will be disabled.');
    }
  }

  // public async sendOtpToPhoneNumber(phoneNumber: string, otp: string, otpTtl: number){
  //   try {
  //     const response = await lastValueFrom(
  //       this.httpService.post(
  //         'https://api.ng.termii.com/api/sms/otp/send',
  //         {
  //           api_key: this.termiiApiKey,
  //           message_type: 'NUMERIC',
  //           to: `234${phoneNumber.substring(1)}`,
  //           from: 'Ogaryde',
  //           channel: 'dnd',
  //           pin_attempts: 3,
  //           pin_time_to_live: otpTtl,
  //           pin_length: 4,
  //           pin_placeholder: '< 1234 >',
  //           message_text: `Your account OTP is: < ${otp} > `,
  //           pin_type: 'NUMERIC',
  //         },
  //         {
  //           headers: {
  //             'Content-Type': 'application/json',
  //           },
  //         },
  //       ),
  //     );
  //
  //     this.logger.log(`OTP sent to phone number ${phoneNumber}`);
  //     return response.data;
  //   } catch (error) {
  //     this.logger.error('Error sending OTP to phone number', error);
  //     CoreUtils.throwError(error);
  //   }
  // }

  /**
   * Verify if a user exists by phone number in the system.
   * @returns boolean - True if user exists, false otherwise.
   * @param pin
   * @param pinId
   */
  // public async verifyOtp(pin: string, pinId: string) {
  //   try {
  //     const data = {
  //       api_key: this.termiiApiKey,
  //       pin_id: pinId,
  //       pin: pin,
  //     };
  //
  //     const response = await lastValueFrom(
  //       this.httpService.post(
  //         `${this.termiiBaseUrl}/api/sms/otp/verify`,
  //         data,
  //         {
  //           headers: {
  //             'Content-Type': 'application/json',
  //           },
  //         },
  //       ),
  //     );
  //
  //     return response.data;
  //   } catch (error) {
  //     this.logger.error('Error verifying OTP', error);
  //     CoreUtils.throwError(error);
  //   }
  // }

  /**
   * Send verification OTP to email.
   * @param email - The email of the user.
   * @param otp - The OTP to send.
   */
  // public async sendOtpToEmail(email: string, otp: string){
  //   try {
  //     const response = await lastValueFrom(
  //       this.httpService.post(
  //         'https://api.ng.termii.com/api/email/otp/send',
  //         {
  //           api_key: this.termiiApiKey,
  //           email_configuration_id: this.termiiConfigId,
  //           email_address: email,
  //           code: otp
  //         },
  //         {
  //           headers: {
  //             'Content-Type': 'application/json',
  //           },
  //         },
  //       ),
  //     );
  //     return response?.data;
  //   } catch (error){
  //     this.logger.error('Error sending OTP to email', error);
  //     CoreUtils.throwError(error)
  //   }
  // }

  async getBalance(): Promise<Stripe.Balance> {
    if (!this.stripe) {
      throw new Error('Stripe is not configured. Please provide STRIPE_API_KEY in environment variables.');
    }

    try {
      const balance = await this.stripe.balance.retrieve();
      this.logger.log('Balance retrieved successfully');
      return balance;
    } catch (error) {
      this.logger.error('Failed to retrieve balance', error.stack);
      throw error;
    }
  }

  // Get Customers
  async getCustomers() {
    if (!this.stripe) {
      throw new Error('Stripe is not configured. Please provide STRIPE_API_KEY in environment variables.');
    }

    try {
      const customers = await this.stripe.customers.list({});
      this.logger.log('Customers fetched successfully');
      return customers.data;
    } catch (error) {
      this.logger.error('Failed to fetch products', error.stack);
      throw error;
    }
  }

  // Accept Payments (Create Payment Intent)
  async createPaymentIntent(
    amount: number,
    currency: string,
  ): Promise<Stripe.PaymentIntent> {
    if (!this.stripe) {
      throw new Error('Stripe is not configured. Please provide STRIPE_API_KEY in environment variables.');
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount,
        currency,
      });
      this.logger.log(
        `PaymentIntent created successfully with amount: ${amount} ${currency}`,
      );
      return paymentIntent;
    } catch (error) {
      this.logger.error('Failed to create PaymentIntent', error.stack);
      throw error;
    }
  }

  // Customer Management (Create Customer)
  async createCustomer(email: string, name: string): Promise<Stripe.Customer> {
    if (!this.stripe) {
      throw new Error('Stripe is not configured. Please provide STRIPE_API_KEY in environment variables.');
    }

    try {
      const customer = await this.stripe.customers.create({ email, name });
      this.logger.log(`Customer created successfully with email: ${email}`);
      return customer;
    } catch (error) {
      this.logger.error('Failed to create customer', error.stack);
      throw error;
    }
  }
}
