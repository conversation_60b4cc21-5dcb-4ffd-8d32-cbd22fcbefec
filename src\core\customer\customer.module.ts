import { LoggerModule } from '@common/logger/logger.module';
import { Customer } from '@core/customer/entities/customer.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerController } from './customer.controller';
import { CustomerMapperService } from './customer.mapper.service';
import { CustomerService } from './customer.service';
import { CustomerValidationService } from './customer.validation.service';

@Module({
  controllers: [CustomerController],
  providers: [CustomerService, CustomerMapperService, CustomerValidationService],
  exports: [CustomerService],
  imports: [
    TypeOrmModule.forFeature([Customer]),
    LoggerModule
  ],
})
export class CustomerModule {}
