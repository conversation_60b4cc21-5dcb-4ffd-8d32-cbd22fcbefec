import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Order } from '@core/order/entities/order.entity';
import { AbstractEntity } from '@common/base.entity';
import { AddressDetails } from '@common/types/index.type';

@Entity({ name: 'shipping' })
export class Shipping extends AbstractEntity{

  @AutoMap()
  @Column({ name: 'carrier', type: 'varchar', length: 50 })
  carrier: string; // e.g., FedEx, UPS, DHL

  @AutoMap()
  @Column({ name: 'tracking_number', type: 'varchar', length: 50 })
  trackingNumber: string; // There's already a function to generate a tracking number. Check CoreUtils for that.

  @AutoMap()
  @Column({ name: 'shipping_cost', type: 'decimal', precision: 10, scale: 2 })
  shippingCost: number; // Cost of shipping

  @AutoMap()
  @Column({ name: 'estimated_delivery_date', type: 'timestamp' })
  estimatedDeliveryDate: Date; // Estimated delivery date for the shipment

  @AutoMap()
  @Column({ name: 'shipping_date', type: 'timestamp' })
  shippingDate: Date; // Date when the item was shipped

  @AutoMap()
  @Column({ name: 'shipping_address', type: 'json' })
  shippingAddress: AddressDetails; // Address where the item is shipped

  @AutoMap(() => Order)
  @OneToOne(() => Order, { eager: true })
  @JoinColumn({ name: 'order_id', referencedColumnName: 'id' })
  product: Order;  // Order associated with the shipment
}
