import { Modu<PERSON> } from '@nestjs/common';
import { CaslAbilityFactoryService } from './casl-ability-factory.service';
import { UnifiedAbilityFactory } from './unified-ability.factory';
import { AuthorizationDomainAbilityFactory } from './authorization-domain.ability.factory';
import { EcommerceDomainAbilityFactory } from './ecommerce-domain.ability.factory';
import { EnhancedAbilityGuard } from './enhanced-ability.guard';
import { ProductAbilityGuard } from './enhanced-ability.guard';
import { OrderAbilityGuard } from './enhanced-ability.guard';
import { ProfileAbilityGuard } from './enhanced-ability.guard';
import { LoggerModule } from '@common/logger/logger.module';
import { I18nModule } from 'nestjs-i18n';

@Module({
  imports: [
    LoggerModule,
    I18nModule,
  ],
  providers: [
    CaslAbilityFactoryService,
    UnifiedAbilityFactory,
    AuthorizationDomainAbilityFactory,
    EcommerceDomainAbilityFactory,
    EnhancedAbilityGuard,
    ProductAbilityGuard,
    OrderAbilityGuard,
    ProfileAbilityGuard,
  ],
  exports: [
    CaslAbilityFactoryService,
    UnifiedAbilityFactory,
    AuthorizationDomainAbilityFactory,
    EcommerceDomainAbilityFactory,
    EnhancedAbilityGuard,
    ProductAbilityGuard,
    OrderAbilityGuard,
    ProfileAbilityGuard,
  ],
})
export class CaslAbilityFactoryModule {} 