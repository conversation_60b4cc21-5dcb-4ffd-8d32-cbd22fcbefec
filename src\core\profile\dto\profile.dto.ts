import { AutoMap } from "@automapper/classes";
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { ApiProperty } from "@nestjs/swagger";
import { EntityDto } from '@common/base.dto';
import { AddressDetails } from '@common/types/index.type';
import { AddressInfoDto } from '@core/profile/dto/address-info.dto';


export class ProfileDto extends EntityDto {
  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    name: "firstName",
    description: "The first name of the user"
  })
  firstName: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    name: "lastName",
    description: "The last name of the user"
  })
  lastName: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    name: "email",
    description: "The email of the user"
  })
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    name: "phoneNumber",
    description: "The phone number of the user"
  })
  phoneNumber: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(Gender),
    name: "gender",
    description: "Possible gender types for users"
  })
  gender: Gender;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(ProfileStatus),
    name: "profileStatus",
    description: "Possible account status for users"
  })
  profileStatus: ProfileStatus;

  @AutoMap(() => String)
  @ApiProperty({
    type: "string",
    enum: Object.values(ProfileType),
    name: "profileType",
    description: "Possible account types for users"
  })
  profileType: ProfileType;

  @AutoMap(() => AddressInfoDto)
  @ApiProperty({
    type: AddressInfoDto,
    name: "address",
    description: "The address details of the user"
  })
  address: AddressDetails;
}
