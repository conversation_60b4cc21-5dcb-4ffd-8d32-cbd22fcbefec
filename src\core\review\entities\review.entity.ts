import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Customer } from '@core/customer/entities/customer.entity';
import { AbstractEntity } from '@common/base.entity';
import { Item } from '@core/item/entities/item.entity';

@Entity({ name: 'review' })
export class Review extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'rating', type: 'int', default: 0 })
  rating: number;

  @AutoMap()
  @Column({ name: 'comment', type: 'text', nullable: true })
  comment: string;

  @OneToOne(() => Item, { eager: true })
  @JoinColumn({ name: 'item_id', referencedColumnName: 'id' })
  item: Item;

  @ManyToOne(() => Customer, (customer) => customer.reviews)
  @JoinColumn({ name: 'customer_id', referencedColumnName: 'id' })
  customer: Customer;
}
