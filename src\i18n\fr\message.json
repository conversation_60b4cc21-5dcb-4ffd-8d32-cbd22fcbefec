{"errors": {"not_found": "{entity} introuvable", "already_exists": "{entity} existe déjà", "ability": "Vous ne pouvez pas supprimer {entity}", "login": "Échec de la connexion", "signup": "Échec de l'inscription", "invalid_credentials": "{field} invalide", "invalid_content": "Contenu invalide", "not_verified": "{entity} n'a pas été vérifié", "suspended": "{entity} a été suspendu", "banned": "{entity} a <PERSON><PERSON> banni", "deactivated": "{entity} a été désactivé", "verify_token_fail": "Impossible de vérifier le token", "recover_password_fail": "Impossible d'envoyer le lien de réinitialisation", "reset_password_fail": "Impossible de réinitialiser le mot de passe", "token_expired": "Le token a expiré. Veuillez réessayer", "recaptcha_failed": "Échec du recaptcha", "register_fail": "{fieldOne} ou {fieldTwo} n'est pas enregistré sur un compte", "account_inactive": "Le compte est inactif", "password_incorrect": "<PERSON><PERSON> de passe incorrect. {attemptsRemaining} tentative(s) restante(s).", "password_mismatch": "Mots de passe ne correspondent pas", "already_verified": "{entity} est déjà vérifié", "configuration_not_found": "Configuration {party} introuvable", "otp_expired": "L'OTP a expiré", "otp_send_fail": "Impossible d'envoyer l'OTP", "otp_verify_fail": "Impossible de vérifier l'OTP", "profile_suspended": "Le profil est suspendu", "profile_banned": "Le profil est banni", "profile_deactivated": "Le profil est désactivé", "invalid_account_type": "Type de compte invalide", "validation_error": "Échec de la validation {entity}", "seed_error": "Échec de l'ensemencement {entity}", "seed_process": "Échec du processus d'ensemencement", "permission_not_found": "Permission introuvable", "permission_already_exists": "Permission existe déjà", "email_taken": "L'email est déjà pris", "tenant_already_exists": "Le locataire existe déjà", "tenant_not_found": "Locataire introuvable", "role_already_exists": "Le rôle existe déjà", "role_not_found": "<PERSON><PERSON><PERSON> introuvable", "cannot_delete": "Vous ne pouvez pas supprimer {entity}", "cannot_delete_critical": "Vous ne pouvez pas supprimer {entity} critique", "restricted_field": "L'accès au champ '{field}' est restreint"}, "success": {"created": "{entity} c<PERSON><PERSON> avec succès", "retrieved": "{entity} récupéré avec succès", "updated": "{entity} mis à jour avec succès", "deleted": "{entity} supprimé avec succès", "activated": "{entity} activé avec succès", "deactivated": "{entity} désactivé avec succès", "ability": "V<PERSON> pouvez supprimer {entity}", "login": "Connexion réussie", "sign_up": "Inscription réussie. Veuillez vérifier votre compte avec l'OTP envoyé par email.", "reset_password": "Mot de passe réinitialisé avec succès", "account_verified": "Compte vérifié avec succès", "verification_otp_resent": "OTP de vérification renvoyé avec succès", "password_reset_otp_sent": "OTP de réinitialisation envoy<PERSON> avec succès", "seeded": "{entity} ensemencé avec succès", "seed_process": "Processus d'ensemencement ré<PERSON>i", "registration": "Inscription réussie", "logout": "Déconnexion réussie", "logout_all_devices": "Déconnecté de tous les appareils", "sessions_retrieved": "Sessions récupérées avec succès", "google_signup": "Inscription Google réussie", "google_signin": "Connexion Google réussie", "tokens_refreshed": "Tokens actualisés avec succès"}}