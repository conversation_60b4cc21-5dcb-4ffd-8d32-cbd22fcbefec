import { Mapper, MappingProfile, createMap } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { CustomerDto } from './dto/customer.dto';
import { Customer } from './entities/customer.entity';

@Injectable()
export class CustomerMapperService extends AutomapperProfile {
  constructor(
      @InjectMapper() mapper: Mapper,
      private readonly logger: LoggerService,
    ) {
      super(mapper);
      this.logger.setContext(CustomerMapperService.name);
    }
  
    override get profile(): MappingProfile {
      return (mapper) => {
        createMap(mapper, Customer, CustomerDto);
        createMap(mapper, CustomerDto, Customer);
      };
    }
}
