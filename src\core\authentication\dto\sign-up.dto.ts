import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';

export class SignUpDto {
  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The first name of the user',
    name: 'firstName',
  })
  firstName: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The last name of the user',
    name: 'lastName',
  })
  lastName: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The email of the user',
    name: 'email',
  })
  email: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The verification code for the user',
    name: 'verifyCode',
  })
  verifyCode: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The phone number of the user',
    name: 'phoneNumber',
  })
  phoneNumber: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The password of the user',
    name: 'password',
  })
  password: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    description: 'The confirmation password of the user',
    name: 'confirmPassword',
  })
  confirmPassword: string;
}
