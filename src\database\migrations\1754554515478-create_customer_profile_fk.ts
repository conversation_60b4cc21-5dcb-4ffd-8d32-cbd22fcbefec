import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateCustomerProfileFk1754554515478 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add a foreign key to the profile table.
        await queryRunner.createForeignKey(
          'customer',
          new TableForeignKey({
              columnNames: ['profile_id'],
              referencedColumnNames: ['id'],
              referencedTableName: 'profile',
              onDelete: 'CASCADE',
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const userTable = await queryRunner.getTable('customer');
        const fk = userTable.foreignKeys.find(
          (fk) => fk.columnNames.indexOf('profile_id') !== -1,
        );
        await queryRunner.dropForeignKey('customer', fk);
    }

}
