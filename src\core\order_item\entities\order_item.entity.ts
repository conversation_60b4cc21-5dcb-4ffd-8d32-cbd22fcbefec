import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { Order } from '@core/order/entities/order.entity';
import { AbstractEntity } from '@common/base.entity';

@Entity({name: 'order_item'})
export class OrderItem extends AbstractEntity {

  @ManyToOne(() => Order, (order) => order.items, { eager: false })
  @JoinColumn({ name: 'cart_id', referencedColumnName: 'id' })
  order: Order;

  @OneToOne(() => Product, { eager: true })
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  product: Product;

  @AutoMap()
  @Column({name: 'quantity', type: 'int'})
  quantity: number;

  @AutoMap()
  @Column({name: 'unit_price', type: 'decimal', precision: 10, scale: 2})
  unitPrice: number;
}
