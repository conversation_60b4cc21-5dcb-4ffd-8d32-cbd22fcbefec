{"errors": {"not_found": "{entity} adịghị", "already_exists": "{entity} d<PERSON> adị", "ability": "Ị nweghị ike i<PERSON> {entity}", "login": "Ịbanye dara", "signup": "<PERSON><PERSON><PERSON><PERSON> aha dara", "invalid_credentials": "{field} ad<PERSON><PERSON><PERSON> mma", "invalid_content": "Ọdịnaya adịghị mma", "not_verified": "{entity} enwegh<PERSON> nkwenye", "suspended": "{entity} akw<PERSON><PERSON>ịla", "banned": "{entity} amachibidoro", "deactivated": "{entity} akw<PERSON><PERSON>", "verify_token_fail": "Enwegh<PERSON> ike <PERSON> token", "recover_password_fail": "Enweghị ike i<PERSON> njikọ <PERSON><PERSON><PERSON><PERSON><PERSON>", "reset_password_fail": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON>", "token_expired": "Token agwụla. <PERSON><PERSON>", "recaptcha_failed": "<PERSON><PERSON><PERSON><PERSON> dara", "register_fail": "{fieldOne} ma <PERSON> b<PERSON> {fieldTwo} enwegh<PERSON> nkwenye na akaụnt<PERSON>", "account_inactive": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar<PERSON>", "password_incorrect": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON> ezi. {attemptsRemaining} mg<PERSON><PERSON> fọ<PERSON>.", "password_mismatch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "already_verified": "{entity} enwe<PERSON><PERSON><PERSON> nkwenye", "configuration_not_found": "<PERSON><PERSON><PERSON> {party} ad<PERSON><PERSON><PERSON>", "otp_expired": "OTP agwụla", "otp_send_fail": "Enweghị ike <PERSON> OTP", "otp_verify_fail": "Enweghị ike <PERSON> OTP", "profile_suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "profile_banned": "Profa<PERSON><PERSON><PERSON>", "profile_deactivated": "<PERSON><PERSON><PERSON><PERSON><PERSON> akw<PERSON><PERSON>", "invalid_account_type": "Ụdị aka<PERSON><PERSON><PERSON> adịghị mma", "validation_error": "Nkwenye {entity} dara", "seed_error": "Ịkụ {entity} dara", "seed_process": "<PERSON><PERSON> dara", "permission_not_found": "<PERSON><PERSON><PERSON>", "permission_already_exists": "Ikike d<PERSON> ad<PERSON>", "email_taken": "<PERSON><PERSON>", "tenant_already_exists": "<PERSON><PERSON> nwe <PERSON>lọ dị adị", "tenant_not_found": "<PERSON><PERSON> nwe <PERSON> ad<PERSON>", "role_already_exists": "Ọrụ dị adị", "role_not_found": "Ọrụ adịghị", "cannot_delete": "Ị nweghị ike i<PERSON> {entity}", "cannot_delete_critical": "Ị nweghị ike i<PERSON> {entity} dị mkpa", "restricted_field": "Ịnweta ubi '{field}' amachibidoro"}, "success": {"created": "E kere {entity} nke <PERSON>ma", "retrieved": "E nwetara {entity} nke <PERSON>ma", "updated": "E melitere {entity} nke <PERSON>ma", "deleted": "E hichapụrụ {entity} nke <PERSON>", "activated": "E kpaliri {entity} nke <PERSON>ma", "deactivated": "E kwụsịrị {entity} nke <PERSON>ma", "ability": "Ị nwere ike i<PERSON> {entity}", "login": "Ịbanye gara nke <PERSON>", "sign_up": "Ndebanye aha gara nke <PERSON>ma. Biko <PERSON> akaụntụ gị na OTP e zipụrụ na email.", "reset_password": "<PERSON> tọghar<PERSON><PERSON><PERSON> ok<PERSON> n<PERSON>", "account_verified": "<PERSON> aka<PERSON><PERSON><PERSON> n<PERSON>", "verification_otp_resent": "E zipụrụ OTP nkwenye ọzọ nke ọma", "password_reset_otp_sent": "E zipụrụ OTP ịtọghar<PERSON> nke <PERSON>", "seeded": "<PERSON> {entity} nke <PERSON>", "seed_process": "Usoro ịkụ gara nke <PERSON>", "registration": "<PERSON><PERSON><PERSON><PERSON> aha gara nke <PERSON>", "logout": "Ọpụpụ gara nke <PERSON>", "logout_all_devices": "Ọpụpụ n'ụgbọ niile gara nke <PERSON>ma", "sessions_retrieved": "E nwetara oge ọrụ nke <PERSON>", "google_signup": "Ndebanye aha Google gara nke ọma", "google_signin": "Ịbanye Google gara nke ọma", "tokens_refreshed": "E melitere tokens nke ọma"}}