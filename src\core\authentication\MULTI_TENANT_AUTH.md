# Multi-Tenant Authentication System

## Overview

The authentication system has been enhanced to support multi-tenant architecture with proper tenant isolation, global user support, and comprehensive session management. The system ensures that users can only access data within their assigned tenants while supporting global users who can access all tenants.

## Multi-Tenant Architecture

### Tenant Isolation Strategy

1. **Database-Level Isolation**: Each entity extends `AbstractEntity` with `tenantId` field
2. **Token-Level Isolation**: JWT tokens include tenant context
3. **Session-Level Isolation**: Redis stores tenant-specific session data
4. **Request-Level Isolation**: Middleware validates tenant access

### Tenant Types

#### 1. Regular Tenant Users
- Users assigned to specific tenants
- Can only access data within their tenant
- Tenant ID is required in authentication requests

#### 2. Global Users
- Users with access to all tenants
- Can switch between tenants
- Used for system administrators and super users

## Authentication Flow with Tenant Context

### 1. User Registration with Tenant
```typescript
POST /api/v1/auth/signup
Headers:
  x-tenant-id: 123
  x-global-user: false

Body:
{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+2347012345678",
  "password": "securePassword123"
}

// Response
{
  "message": "Registration successful",
  "data": null
}
```

### 2. User Login with Tenant Context
```typescript
POST /api/v1/auth/login
Headers:
  x-tenant-id: 123
  x-global-user: false

Body:
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

// Response
{
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "profileInfo": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "tenantId": 123
    },
    "tenantId": 123,
    "isGlobalUser": false
  }
}
```

### 3. Global User Login
```typescript
POST /api/v1/auth/login
Headers:
  x-global-user: true

Body:
{
  "email": "<EMAIL>",
  "password": "adminPassword123"
}

// Response
{
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "profileInfo": {
      "id": 1,
      "firstName": "Admin",
      "lastName": "User",
      "email": "<EMAIL>",
      "tenantId": null
    },
    "tenantId": null,
    "isGlobalUser": true
  }
}
```

## Token Structure with Tenant Context

### Access Token Payload
```typescript
{
  sub: userId,           // User ID
  email: string,         // User email
  tenantId?: number,     // Tenant ID (optional for global users)
  isGlobalUser: boolean, // Whether user is global
  type: 'access',        // Token type
  iat: number,           // Issued at timestamp
  exp: number            // Expiration timestamp
}
```

### Refresh Token Payload
```typescript
{
  sub: userId,           // User ID
  email: string,         // User email
  tenantId?: number,     // Tenant ID (optional for global users)
  isGlobalUser: boolean, // Whether user is global
  type: 'refresh',       // Token type
  iat: number,           // Issued at timestamp
  exp: number            // Expiration timestamp
}
```

## Tenant Validation

### 1. Tenant Access Validation
```typescript
private async validateTenantAccess(profile: Profile, tenantId: number): Promise<void> {
  // Check if user belongs to the specified tenant
  if (profile.tenantId && profile.tenantId !== tenantId) {
    throw new ForbiddenException('User does not have access to this tenant');
  }

  // Additional tenant-specific validation can be added here
  // For example, checking if the tenant is active, if user has specific permissions, etc.
}
```

### 2. JWT Strategy Validation
```typescript
async validate(payload: TenantAwareJwtPayload): Promise<Profile> {
  const { email, tenantId, isGlobalUser, type } = payload;
  
  // Validate token type
  if (type !== 'access') {
    throw new Error('Invalid token type');
  }

  const profile = await this.profileService.findProfileByEmail(email);
  await this.profileService.checkProfileEligibility(profile);

  // Validate tenant access for non-global users
  if (!isGlobalUser && tenantId && profile.tenantId && profile.tenantId !== tenantId) {
    throw new Error('User does not have access to this tenant');
  }

  // Add tenant context to profile for downstream use
  (profile as any).currentTenantId = tenantId;
  (profile as any).isGlobalUser = isGlobalUser;

  return profile;
}
```

## Session Management with Tenant Isolation

### Redis Key Structure
```typescript
// Token metadata
`token:access:${accessToken}`     // Access token metadata
`token:refresh:${refreshToken}`   // Refresh token metadata

// User sessions per tenant
`sessions:${userId}:${tenantId}`  // User sessions in specific tenant
`sessions:${userId}:global`       // Global user sessions

// Token blacklist
`blacklist:${refreshToken}`       // Blacklisted refresh tokens
```

### Session Storage
```typescript
const tokenMetadata = {
  userId,
  tenantId,
  isGlobalUser,
  createdAt: new Date().toISOString(),
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
};

// Store access token metadata
await this.redisService.set(
  `token:access:${accessToken}`, 
  JSON.stringify(tokenMetadata), 
  15 * 60 // 15 minutes
);

// Store refresh token metadata
await this.redisService.set(
  `token:refresh:${refreshToken}`, 
  JSON.stringify(tokenMetadata), 
  7 * 24 * 60 * 60 // 7 days
);
```

## API Endpoints with Tenant Support

### Public Endpoints

| Endpoint | Method | Headers | Description |
|----------|--------|---------|-------------|
| `/api/v1/auth/signup` | POST | `x-tenant-id`, `x-global-user` | User registration with tenant |
| `/api/v1/auth/login` | POST | `x-tenant-id`, `x-global-user` | User login with tenant context |
| `/api/v1/auth/refresh` | POST | `x-tenant-id`, `x-global-user` | Refresh tokens with tenant context |
| `/api/v1/auth/verify-account` | POST | - | Verify account with OTP |
| `/api/v1/auth/resend-verification-otp` | POST | - | Resend verification OTP |
| `/api/v1/auth/forgot-password` | POST | - | Request password reset |
| `/api/v1/auth/reset-password` | POST | - | Reset password with OTP |

### Protected Endpoints

| Endpoint | Method | Headers | Description |
|----------|--------|---------|-------------|
| `/api/v1/auth/logout` | POST | `x-tenant-id` | User logout with tenant context |
| `/api/v1/auth/logout-all-devices` | POST | `x-tenant-id` | Logout from all devices in tenant |
| `/api/v1/auth/sessions` | GET | `x-tenant-id` | Get active sessions in tenant |

## Tenant Middleware Integration

### Request Flow
```
Request → TenantMiddleware → ProfileContextInterceptor → Authentication → Authorization
   ↓              ↓                        ↓                    ↓              ↓
Extract      Set Tenant        Set User & Tenant    Validate    Check
Tenant ID    Context          Context in Request   Tokens      Permissions
```

### Tenant Middleware
```typescript
@Injectable()
export class TenantMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const tenantId = req.header('x-tenant-id');
    const isGlobalUser = req.header('x-global-user') === 'true';

    if (!tenantId && !isGlobalUser) {
      return res.status(HttpStatus.BAD_REQUEST).json({ 
        message: 'Missing tenant context' 
      });
    }

    req['tenantId'] = tenantId;
    req['isGlobalUser'] = isGlobalUser;
    next();
  }
}
```

## Security Features

### 1. Tenant Isolation
- **Database Level**: All entities have `tenantId` field
- **Token Level**: JWT tokens include tenant context
- **Session Level**: Redis stores tenant-specific sessions
- **Request Level**: Middleware validates tenant access

### 2. Global User Support
- Global users can access all tenants
- Tenant context is optional for global users
- Global users can switch between tenants

### 3. Token Validation
- Tokens include tenant context
- Token type validation (access vs refresh)
- Tenant access validation on each request

### 4. Session Management
- Tenant-specific session storage
- Session limits per tenant (10 sessions per user per tenant)
- Automatic session cleanup

## Usage Examples

### 1. Regular Tenant User
```typescript
// Login to specific tenant
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-tenant-id': '123',
    'x-global-user': 'false'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { accessToken, refreshToken, tenantId } = await response.json();

// Use access token for API requests
const apiResponse = await fetch('/api/v1/profile', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'x-tenant-id': '123'
  }
});
```

### 2. Global User
```typescript
// Login as global user
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-global-user': 'true'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'adminPassword123'
  })
});

const { accessToken, refreshToken, isGlobalUser } = await response.json();

// Access any tenant
const apiResponse = await fetch('/api/v1/profile', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'x-tenant-id': '456' // Can access any tenant
  }
});
```

### 3. Token Refresh with Tenant Context
```typescript
// Refresh tokens maintaining tenant context
const response = await fetch('/api/v1/auth/refresh', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${refreshToken}`,
    'x-tenant-id': '123',
    'x-global-user': 'false'
  }
});

const { accessToken, refreshToken } = await response.json();
```

## Error Handling

### Common Tenant-Related Errors

#### 1. Missing Tenant Context
```json
{
  "statusCode": 400,
  "message": "Missing tenant context",
  "error": "Bad Request"
}
```

#### 2. Tenant Access Denied
```json
{
  "statusCode": 403,
  "message": "User does not have access to this tenant",
  "error": "Forbidden"
}
```

#### 3. Invalid Token Type
```json
{
  "statusCode": 401,
  "message": "Invalid token type",
  "error": "Unauthorized"
}
```

## Best Practices

### 1. Tenant Headers
- Always include `x-tenant-id` for tenant-specific operations
- Use `x-global-user: true` for global user operations
- Validate tenant headers in middleware

### 2. Token Management
- Store tokens with tenant context
- Validate tenant access on each request
- Use tenant-specific session management

### 3. Database Queries
- Always filter by `tenantId` for tenant users
- Allow global users to access all tenants
- Use repository context for tenant isolation

### 4. Security
- Validate tenant access at multiple levels
- Use Redis for tenant-specific session storage
- Implement proper token blacklisting per tenant

## Monitoring and Logging

### Tenant-Specific Logs
```typescript
// Log tenant context in all operations
logger.log(`User ${email} logged in to tenant ${tenantId}`);

// Log tenant access violations
logger.warn(`User ${email} attempted to access tenant ${tenantId} without permission`);

// Log global user operations
logger.log(`Global user ${email} accessed tenant ${tenantId}`);
```

### Session Monitoring
```typescript
// Monitor active sessions per tenant
const tenantSessions = await authService.getActiveSessions(userId, tenantId);
logger.log(`User ${userId} has ${tenantSessions.length} active sessions in tenant ${tenantId}`);
```

## Performance Considerations

1. **Redis Optimization**: Use tenant-specific keys for better performance
2. **Database Indexing**: Index `tenantId` field for faster queries
3. **Caching**: Cache tenant-specific data separately
4. **Connection Pooling**: Use tenant-aware connection pooling

## Future Enhancements

1. **Tenant-Specific Configuration**: Different settings per tenant
2. **Cross-Tenant Operations**: Limited cross-tenant data sharing
3. **Tenant Analytics**: Usage analytics per tenant
4. **Tenant Migration**: Tools for moving users between tenants
5. **Tenant Backup**: Tenant-specific backup and restore 