import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { accounts, countries, permissions, roles } from './data';
import { Role } from '@core/authorization/role/entities/role.entity';
import { CoreConstants } from '@common/utils/core.constants';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { Tenant } from '@core/tenant/entities/tenant.entity';

@Injectable()
export class EntitiesSeederService {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Profile)
    private readonly profileRepository: Repository<Profile>,
    @InjectRepository(Permission)
    private readonly permissionRepo: Repository<Permission>,
    @InjectRepository(Role)
    private readonly roleRepo: Repository<Role>,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
    ) {
    this.logger.setContext(EntitiesSeederService.name)
  }

  async seed(): Promise<void>{
    await this.seedPermissions();
    await this.seedRoles();
    await this.seedAccounts();
    await this.seedTenants();
    this.logger.log('Seeding completed.');
  }

  private async seedPermissions(): Promise<void> {
    try {
      const createdPermissions = await Promise.all(
        permissions.map((permission) =>
          this.permissionRepo.save(
            this.permissionRepo.create(permission),
          ),
        ),
      );
      this.logger.log(`Created ${createdPermissions.length} permissions.`);
    } catch (error) {
      this.logger.error('Something went wrong while seeding permissions', error.stack);
      throw new Error(error.stack);
    }
  }

  private async seedRoles(): Promise<void>{
    try {
      const allPermissions = await this.permissionRepo.find();
      const createdRoles = await Promise.all(
        roles.map((role) =>
          this.roleRepo.save(
            this.roleRepo.create({
              ...role,
              permissions: allPermissions,
            }),
          ),
        ),
      );
      this.logger.log(`Created ${createdRoles.length} roles.`);
    } catch (error) {
      this.logger.error('Something went wrong while seeding roles', error.stack);
      throw new Error(error.stack)
    }
  }

  private async seedAccounts(): Promise<void>{
    try {
      const adminRole = await this.roleRepo.findOne({
        where: { name: CoreConstants.SUPER_ADMIN_ROLE },
        relations: ['permissions'],
      });
      const createdAccounts = await Promise.all(
        accounts.map((account) =>
          this.profileRepository.save(
            this.profileRepository.create({
              firstName: account.firstName,
              lastName: account.lastName,
              email: account.email,
              authInfo: {
                password: account.password,
                failedLoginCount: 0,
                passwordResetDate: null,
                refreshToken: null,
              },
              verificationInfo: {
                verified: true,
                kycVerified: true,
              },
              phoneNumber: account.phoneNumber,
              role: adminRole,
              profileStatus: ProfileStatus.ACTIVE,
              profileType: ProfileType.STAFF,
              gender: Gender.DEFAULT,

            }),
          ),
        ),
      );
      this.logger.log(`Created ${createdAccounts.length} accounts.`);
    } catch (error) {
      this.logger.error('Something went wrong while seeding accounts', error.stack);
      throw new Error(error.stack);
    }
  }

  private async seedTenants(): Promise<void> {
    let countTenant: number = 0;
    for (const tenant of countries) {
      try {
        const existingTenant = await this.tenantRepo.findOne({ where: { name: tenant.name } });

        if (!existingTenant) {
          const newTenant = this.tenantRepo.create({
            name: tenant.name,
            code: tenant.code,
            priceFactor: tenant.priceFactor,
            currencyCode: tenant.currencyCode,
            currencySymbol: tenant.currencySymbol,
          });
          await this.tenantRepo.save(newTenant);
          countTenant++;
        }
      } catch (error) {
        this.logger.error(`Error creating tenant ${tenant.name}: ${error}`);
        throw new InternalServerErrorException(`Error creating tenant ${tenant.name}: ${error}`);
      }
    }
  }
}
