import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { Tenant } from './entities/tenant.entity';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { TenantValidationService } from './tenant.validation.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { EntityStatus } from '@common/base.entity';
import { BaseRepository } from '@common/base.repository';

@Injectable()
export class TenantService implements EntityServiceStrategy<Tenant> {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Tenant)
    private readonly tenantRepository: BaseRepository<Tenant>,
    private readonly tenantValidator: TenantValidationService,
  ) {
    this.logger.setContext(TenantService.name);
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const tenant = await this.tenantRepository.findOne({ where: { id } });
        tenant.status = EntityStatus.ACTIVE;
        await this.tenantRepository.save(tenant);
      }),
    );
  }

  async create(data: Tenant): Promise<Tenant> {
    await this.tenantValidator.validate(data, DatabaseAction.CREATE);
    return await this.tenantRepository.save(data);
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const tenant = await this.tenantRepository.findOne({ where: { id: id } });
        tenant.status = EntityStatus.INACTIVE;
        await this.tenantRepository.save(tenant);
      }),
    );
  }

  async findByPk(id: number): Promise<Tenant | null> {
    return Promise.resolve(
      await this.tenantRepository.findOne({ where: { id: id } }),
    );
  }

  async modify(id: number, data: Tenant): Promise<Tenant> {
    await this.tenantValidator.validate(data, DatabaseAction.UPDATE);

    if (!(await this.findByPk(id)))
      throw new NotFoundException(
        this.i18n.t('errors.tenant_not_found'),
      );
    return Promise.resolve(await this.tenantRepository.save(data));

  }
}
