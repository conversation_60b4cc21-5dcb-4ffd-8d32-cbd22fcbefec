import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class GoogleAuthCallbackQueryDto {
  @ApiProperty({
    description: 'JWT access token',
    type: String,
    name: 'accessToken',
  })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    type: String,
    name: 'refreshToken',
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'Whether this is a new user registration',
    required: false,
    type: String,
    name: 'isNewUser',
  })
  @IsString()
  isNewUser: boolean;

  @ApiProperty({
    description: 'User email',
    type: String,
    name: 'email',
  })
  @IsString()
  email: string;
}
