import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateRoleProfileFk1754554044700 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add a foreign key to the profile table.
        await queryRunner.createForeignKey(
          'profile',
          new TableForeignKey({
              columnNames: ['role_id'],
              referencedColumnNames: ['id'],
              referencedTableName: 'role',
              onDelete: 'CASCADE',
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      const userTable = await queryRunner.getTable('profile');
      const fk = userTable.foreignKeys.find(
        (fk) => fk.columnNames.indexOf('role_id') !== -1,
      );
      await queryRunner.dropForeignKey('profile', fk);
    }

}
