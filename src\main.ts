import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from '@common/filters/http-exception.filter';

async function bootstrap() {
  const logger = new Logger('Application');

  // Optimize for production memory usage
  const app = await NestFactory.create(AppModule, {
    logger: process.env.NODE_ENV === 'production'
      ? ['error', 'warn', 'log']
      : ['error', 'warn', 'log', 'debug', 'verbose']
  });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  app.enableCors();
  app.setGlobalPrefix('api/');
  app.useGlobalFilters(new HttpExceptionFilter());

  app.enableVersioning({
    type: VersioningType.URI,
  });

  const configService = app.get(ConfigService);

  // Only setup Swagger in non-production environments to save memory
  if (process.env.NODE_ENV !== 'production') {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Green Pasture Backend System')
      .setDescription('This is a RESTful web service for the Green application')
      .setVersion('1.0.0')
      .setContact(
        'Support Team',
        'https://greenpasture.com/support',
        '<EMAIL>',
      )
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api-docs', app, document);

    app.getHttpAdapter().get('/api-docs-json', (req, res) => {
      res.json(document);
    });
  }

  // CRITICAL: Get port from environment (Render sets this)
  const port = process.env.PORT || configService.get<string>('port') || 3000;

  // CRITICAL: Bind to 0.0.0.0 for Render
  await app.listen(port, '0.0.0.0');

  logger.log(`Application running on port ${port}`);
}

bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('Error starting the application:', error);
  process.exit(1); // Exit on bootstrap failure
});
