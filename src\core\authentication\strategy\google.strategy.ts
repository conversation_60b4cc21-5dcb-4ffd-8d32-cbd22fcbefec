import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { LoggerService } from '@common/logger/logger.service';

export interface IGoogleProfile {
  id: string | number;
  firstName?: string;
  lastName?: string;
  email: string;
  picture: string;
}

/*
    Follow this tutorial to get required credentials:
    https://dev.to/imichaelowolabi/how-to-implement-login-with-google-in-nest-js-2aoa

	Follow this tutorial for learning more:
	https://blog.logrocket.com/implement-secure-single-sign-on-nestjs-google/
*/

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new LoggerService(GoogleStrategy.name);
  constructor(
    private configService: ConfigService,
  ) {
    super({
      clientID: configService.get<string>('oauth.google.clientId'),
      clientSecret: configService.get<string>('oauth.google.secret'),
      callbackURL: `http://localhost:${configService.get<string>(
        'port',
      )}/auth/google/callback`,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    _accessToken: string,
    _refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      this.logger.log(`Google OAuth validation for profile: ${profile.id}`);

      const { id, name, emails, photos } = profile;

      // Extract essential profile information
      const googleProfile: IGoogleProfile  = {
        id: id,
        email: emails[0].value,
        firstName: name.givenName,
        lastName: name.familyName,
        picture: photos[0].value,
      };

      done(null, googleProfile);
    } catch (error) {
      this.logger.error(`Google OAuth validation failed: ${error.message}`, error.stack);
      done(error, null);
    }
  }
}
