# Enhanced CASL Ability System

This directory contains an enhanced, modular CASL ability system that provides robust authorization capabilities across multiple domains.

## Overview

The enhanced CASL ability system is designed to be:

- **Modular**: Each domain has its own ability factory
- **Flexible**: Easy to add new domains and customize permissions
- **Type-safe**: Full TypeScript support with proper type inference
- **Extensible**: Simple to extend with new functionality
- **Backward compatible**: Existing code continues to work

## Architecture

### Core Components

1. **BaseDomainAbilityFactory**: Abstract base class for all domain factories
2. **DomainAbilityFactory Interface**: Contract for domain-specific factories
3. **UnifiedAbilityFactory**: Combines all domain abilities into a unified system
4. **EnhancedAbilityGuard**: Advanced guard for permission checking
5. **Decorators**: Easy-to-use decorators for common permission checks

### Domain Factories

- **AuthorizationDomainAbilityFactory**: Handles Profile, Role, Permission entities
- **EcommerceDomainAbilityFactory**: Handles Product, Order, Cart, Review entities
- **Custom Domain Factories**: Easy to create for new domains

## Usage

### Basic Usage

```typescript
// In a controller
@Controller('products')
@UseGuards(EnhancedAbilityGuard)
export class ProductController {
  @Get()
  @CheckRead('Product')
  async findAll() {
    return { message: 'Products retrieved successfully' };
  }

  @Post()
  @CheckCreate('Product')
  async create(@Body() createProductDto: any) {
    return { message: 'Product created successfully' };
  }
}
```

### Field-Level Permissions

```typescript
@Put(':id/price')
@CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'price' })
async updatePrice(@Param('id') id: string, @Body() priceData: any) {
  return { message: 'Product price updated successfully' };
}
```

### Multiple Permissions

```typescript
@Post(':id/review')
@CheckAbility(
  { action: UserActions.READ, subject: 'Product' },
  { action: UserActions.CREATE, subject: 'Review' }
)
async createReview(@Param('id') productId: string, @Body() reviewData: any) {
  return { message: 'Review created successfully' };
}
```

### Service-Level Usage

```typescript
export class ProductService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
  ) {}

  async createProduct(user: any, productData: any) {
    const ability = this.caslAbilityFactory.createForUser(user);

    if (!ability.can(UserActions.CREATE, 'Product')) {
      throw new Error('User cannot create products');
    }

    // Proceed with product creation
    return { message: 'Product created successfully' };
  }
}
```

## Creating Custom Domain Factories

### Step 1: Extend BaseDomainAbilityFactory

```typescript
@Injectable()
export class CustomDomainAbilityFactory extends BaseDomainAbilityFactory {
  getDomainName(): string {
    return 'custom';
  }

  getSubjects(): any[] {
    return [CustomEntity1, CustomEntity2];
  }

  getSubjectTypes(): any[] {
    return [CustomEntity1, CustomEntity2];
  }

  protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
    if (profile.profileType === ProfileType.STAFF) {
      ability.can(UserActions.MANAGE, 'all');
    } else {
      // Apply customer-specific rules
      ability.can(UserActions.READ, 'CustomEntity', { userId: profile.id });
    }
  }
}
```

### Step 2: Register the Factory

```typescript
// In your module
@Module({
  providers: [
    CustomDomainAbilityFactory,
    // ... other providers
  ],
})
export class CustomModule {}

// Or register dynamically
this.caslAbilityFactory.registerDomainFactory('custom', customFactory);
```

## Available Decorators

### Basic Decorators

- `@CheckCreate(subject, field?)`: Check create permission
- `@CheckRead(subject, field?)`: Check read permission
- `@CheckUpdate(subject, field?)`: Check update permission
- `@CheckDelete(subject, field?)`: Check delete permission
- `@CheckManage(subject, field?)`: Check manage permission

### Domain-Specific Decorators

- `@CheckProductAbility(action, field?)`: Product-specific checks
- `@CheckOrderAbility(action, field?)`: Order-specific checks
- `@CheckProfileAbility(action, field?)`: Profile-specific checks
- `@CheckRoleAbility(action, field?)`: Role-specific checks

### Advanced Decorator

- `@CheckAbility(...requirements)`: Custom ability checks with full control

## Permission Structure

### User Actions

```typescript
export enum UserActions {
  MANAGE = 'manage',
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
}
```

### Permission Components

1. **Action**: What the user can do (create, read, update, delete, manage)
2. **Subject**: What the user can act on (Product, Order, Profile, etc.)
3. **Field**: Optional field restriction (price, status, etc.)
4. **Conditions**: Optional conditions for the permission

## Domain-Specific Features

### Authorization Domain

- Profile management with role-based access
- Role and permission management
- Field-level restrictions for sensitive data
- Staff vs customer permission differentiation

### E-commerce Domain

- Product management with inventory controls
- Order management with status restrictions
- Cart and review management
- Field-level pricing and inventory permissions

## Advanced Features

### Cross-Domain Permissions

```typescript
const unifiedAbility = this.caslAbilityFactory.createForUser(user);

// Check permissions across multiple domains
const canManageProducts = unifiedAbility.can(UserActions.MANAGE, 'Product');
const canManageOrders = unifiedAbility.can(UserActions.MANAGE, 'Order');
```

### Domain-Specific Abilities

```typescript
const domainAbility = this.caslAbilityFactory.getDomainAbility(user, 'ecommerce');
const builtAbility = domainAbility.build();
```

### Available Domains

```typescript
const availableDomains = this.caslAbilityFactory.getAvailableDomains();
// Returns: ['authorization', 'ecommerce']
```

## Migration from Legacy System

### Before (Legacy)

```typescript
// Old way
const ability = this.caslAbilityFactory.createLegacyForUser(profile);
```

### After (Enhanced)

```typescript
// New way - same method name, enhanced functionality
const ability = this.caslAbilityFactory.createForUser(profile);

// Or use legacy method for backward compatibility
const legacyAbility = this.caslAbilityFactory.createLegacyForUser(profile);
```

## Best Practices

1. **Use Domain-Specific Decorators**: They provide better type safety and readability
2. **Implement Field-Level Permissions**: For sensitive data like pricing and status
3. **Use Conditions**: For user-specific data access
4. **Create Custom Domain Factories**: For new business domains
5. **Log Permission Checks**: For debugging and auditing
6. **Handle Errors Gracefully**: Provide meaningful error messages

## Error Handling

The system provides comprehensive error handling:

```typescript
try {
  const canPerform = ability.can(action, subject, field, conditions);
  if (!canPerform) {
    throw new ForbiddenException('Insufficient permissions');
  }
} catch (error) {
  // Handle permission errors
  this.logger.error(`Permission check failed: ${error.message}`);
  throw new ForbiddenException('Access denied');
}
```

## Testing

### Unit Testing Domain Factories

```typescript
describe('AuthorizationDomainAbilityFactory', () => {
  let factory: AuthorizationDomainAbilityFactory;

  beforeEach(() => {
    factory = new AuthorizationDomainAbilityFactory(logger, i18n);
  });

  it('should allow staff to manage all', () => {
    const staffProfile = createStaffProfile();
    const ability = factory.createForUser(staffProfile);
    
    expect(ability.can(UserActions.MANAGE, 'all')).toBe(true);
  });
});
```

### Integration Testing

```typescript
describe('EnhancedAbilityGuard', () => {
  it('should allow authorized users', async () => {
    const request = createMockRequest(authorizedUser);
    const context = createMockContext(request);
    
    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });
});
```

## Performance Considerations

1. **Caching**: Consider caching abilities for frequently accessed users
2. **Lazy Loading**: Load domain factories only when needed
3. **Optimization**: Use specific domain abilities instead of unified when possible
4. **Monitoring**: Log performance metrics for permission checks

## Security Considerations

1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Field-Level Security**: Restrict access to sensitive fields
3. **Audit Logging**: Log all permission checks and denials
4. **Regular Reviews**: Periodically review and update permissions
5. **Input Validation**: Validate all permission parameters

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check user roles and permissions
2. **Domain Not Found**: Ensure domain factory is registered
3. **Type Errors**: Verify subject types match domain definitions
4. **Performance Issues**: Consider caching or domain-specific abilities

### Debug Mode

Enable debug logging to see detailed permission checks:

```typescript
// In your configuration
this.logger.setLogLevel('debug');
```

## Contributing

When adding new domains or features:

1. Extend `BaseDomainAbilityFactory`
2. Implement required abstract methods
3. Add appropriate decorators
4. Update documentation
5. Add tests
6. Register in the module

## Support

For issues or questions:

1. Check the usage examples
2. Review the domain factory implementations
3. Check the test files for examples
4. Consult the CASL documentation 