import { ApiProperty } from '@nestjs/swagger';

export class PaginatedResponseDto<T> {
  @ApiProperty({ example: 1 })
  totalItems: number;

  @ApiProperty({ example: 1 })
  itemCount: number;

  @ApiProperty({ example: 10 })
  itemsPerPage: number;

  @ApiProperty({ example: 1 })
  totalPages: number;

  @ApiProperty({ example: 1 })
  currentPage: number;

  @ApiProperty({ type: [Object] })
  items: T[];

  @ApiProperty({
    example: {
      first: `/api/state?limit=10`,
      previous: '',
      next: '',
      last: `/api/state?page=1&limit=10`,
    },
  })
  links: {
    first: string;
    previous: string;
    next: string;
    last: string;
  };
}
