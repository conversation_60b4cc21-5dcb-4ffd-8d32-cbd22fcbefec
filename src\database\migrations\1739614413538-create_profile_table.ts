import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

/*
    This file was created with the following cli command:
    npm run typeorm:create-migration --name=create_user

    Find a list of all migration methods and usages at:
    https://typeorm.io/migrations
*/
export class CreateProfileTable1739614413538 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'profile',
        columns: [
          { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
          { name: 'tenant_id', type: 'bigint', isNullable: true },
          { name: 'status', type: 'varchar' },
          { name: 'created_at', type: 'timestamp', default: 'now()' },
          { name: 'created_by', type: 'varchar' },
          { name: 'updated_at', type: 'timestamp', default: 'now()' },
          { name: 'updated_by', type: 'varchar', isNullable: true },
          { name: 'deleted_at', type: 'timestamp', isNullable: true },
          { name: 'first_name', type: 'varchar' },
          { name: 'last_name', type: 'varchar', isNullable: true },
          { name: 'email', type: 'varchar', isUnique: true },
          { name: 'phone_number', type: 'varchar', isUnique: true },
          { name: 'gender', type: 'varchar' },
          { name: 'profile_status', type: 'varchar' },
          { name: 'profile_type', type: 'varchar' },
          { name: 'auth_info', type: 'jsonb', isNullable: true },
          { name: 'verification_info', type: 'jsonb', isNullable: true },
          { name: 'address', type: 'jsonb', isNullable: true },
          { name: 'settings', type: 'jsonb', isNullable: true },
          { name: 'role_id', type: 'bigint', isNullable: true },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'profile',
      new TableIndex({
        name: 'IDX_PROFILE_FIELDS',
        columnNames: [
          'id',
          'status',
          'created_at',
          'updated_at',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('profile', 'IDX_PROFILE_FIELDS');
    await queryRunner.dropTable('profile');
  }
}
