import { Modu<PERSON> } from '@nestjs/common';
import { RoleModule } from './role/role.module';
import { PermissionModule } from './permission/permission.module';
import { CaslAbilityFactoryService } from './casl-ability-factory/casl-ability-factory.service';
import { UnifiedAbilityFactory } from './casl-ability-factory/unified-ability.factory';
import { AuthorizationDomainAbilityFactory } from './casl-ability-factory/authorization-domain.ability.factory';
import { EcommerceDomainAbilityFactory } from './casl-ability-factory/ecommerce-domain.ability.factory';
import { LoggerModule } from '@common/logger/logger.module';
import { PermissionsGuard } from 'src/core/authorization/permission/permission.guard';

@Module({
  imports: [RoleModule, PermissionModule, LoggerModule],
  providers: [
    CaslAbilityFactoryService, 
    UnifiedAbilityFactory,
    AuthorizationDomainAbilityFactory,
    EcommerceDomainAbilityFactory,
    PermissionsGuard
  ],
  exports: [CaslAbilityFactoryService, PermissionsGuard],
})
export class AuthorizationModule {}
