import { Injectable } from "@nestjs/common";
import { EntitiesSeederService } from "@database/seeders/entities-seeder/entities-seeder.service";
import { LoggerService } from '@common/logger/logger.service';

@Injectable()
export class SeederService {
  constructor(
    private readonly seederSvc: EntitiesSeederService,

    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(SeederService.name);
  }


  async seed(): Promise<void>{
    try{
      await this.seederSvc.seed();
      this.logger.log('Seeding completed successfully');
    } catch(error){
      this.logger.error('Seeding failed', error.stack);
      throw new Error(`Seeding failed: ${error.stack}`);
    }
  }
}
