import { Column, Entity } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AddressDetails } from '@common/types/index.type';
import { AbstractEntity } from '@common/base.entity';

@Entity({ name: 'store' })
export class Store extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', length: 100 })
  name: string;

  @AutoMap()
  @Column({name: 'address', type: 'jsonb', nullable: true})
  address: AddressDetails;
}
