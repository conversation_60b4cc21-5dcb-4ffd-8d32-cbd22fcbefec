import { Injectable } from '@nestjs/common';
import { AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects, PureAbility } from '@casl/ability';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { AuthorizationDomainAbilityFactory } from './authorization-domain.ability.factory';
import { EcommerceDomainAbilityFactory } from './ecommerce-domain.ability.factory';
import { DomainAbilityFactory } from './domain-ability.interface';

// Import all entities for type inference
import { Role } from '@core/authorization/role/entities/role.entity';
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { Product } from '@core/product/entities/product.entity';
import { Order } from '@core/order/entities/order.entity';
import { Cart } from '@core/cart/entities/cart.entity';
import { CartItem } from '@core/cart_item/entities/cart_item.entity';
import { Review } from '@core/review/entities/review.entity';
import { Customer } from '@core/customer/entities/customer.entity';
import { Staff } from '@core/staff/entities/staff.entity';
import { Store } from '@core/store/entities/store.entity';
import { Shipping } from '@core/shipping/entities/shipping.entity';
import { Notification } from '@core/notification/entities/notification.entity';

/**
 * Defines all subjects for the unified ability across all domains.
 * 
 * This type includes all entities from all domains in the application,
 * providing comprehensive type safety for permission checks.
 * 
 * @example
 * ```typescript
 * const ability: UnifiedAbility = factory.createForUser(profile);
 * const canReadProduct = ability.can('read', 'Product');
 * const canManageOrder = ability.can('manage', 'Order');
 * ```
 */
export type UnifiedSubjects = InferSubjects<
  | typeof Profile
  | typeof Role
  | typeof Permission
  | typeof Product
  | typeof Order
  | typeof Cart
  | typeof CartItem
  | typeof Review
  | typeof Customer
  | typeof Staff
  | typeof Store
  | typeof Shipping
  | typeof Notification
> | 'all';

/**
 * Unified CASL ability that can handle all subjects across all domains.
 * 
 * This ability type provides comprehensive permission checking capabilities
 * for the entire application, combining rules from all domain factories.
 * 
 * @example
 * ```typescript
 * const ability: UnifiedAbility = factory.createForUser(profile);
 * const canManageAll = ability.can('manage', 'all');
 * ```
 */
export type UnifiedAbility = PureAbility<[UserActions, UnifiedSubjects]>;

/**
 * Factory for creating unified CASL abilities that combine permissions from all domains.
 * 
 * This factory orchestrates the creation of comprehensive abilities by combining
 * rules from multiple domain-specific factories. It provides a single entry point
 * for creating abilities that can handle permissions across the entire application.
 * 
 * Key Features:
 * - Combines abilities from multiple domain factories
 * - Provides domain-specific ability access
 * - Applies global restrictions across all domains
 * - Supports dynamic domain factory registration
 * - Comprehensive error handling and logging
 * 
 * Architecture:
 * - Uses a Map to store domain factories
 * - Applies rules from all registered domains
 * - Combines abilities into a unified ability
 * - Provides domain-specific access methods
 * 
 * @example
 * ```typescript
 * const factory = new UnifiedAbilityFactory(logger, i18n, authFactory, ecommerceFactory);
 * const unifiedAbility = factory.createForUser(profile);
 * const canManageProducts = unifiedAbility.can('manage', 'Product');
 * 
 * // Domain-specific access
 * const authAbility = factory.getDomainAbility(profile, 'authorization');
 * const ecommerceAbility = factory.getDomainAbility(profile, 'ecommerce');
 * ```
 */
@Injectable()
export class UnifiedAbilityFactory {
  private readonly domainFactories: Map<string, DomainAbilityFactory> = new Map();

  /**
   * Creates a new UnifiedAbilityFactory instance.
   * 
   * @param logger - Logger service for debugging and monitoring
   * @param i18n - Internationalization service for error messages
   * @param authorizationDomainFactory - Factory for authorization domain abilities
   * @param ecommerceDomainFactory - Factory for e-commerce domain abilities
   */
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    private readonly authorizationDomainFactory: AuthorizationDomainAbilityFactory,
    private readonly ecommerceDomainFactory: EcommerceDomainAbilityFactory,
  ) {
    this.logger.setContext(UnifiedAbilityFactory.name);
    this.registerDomainFactories();
  }

  /**
   * Registers the default domain factories.
   * 
   * This method sets up the initial domain factories for the application.
   * Additional factories can be registered dynamically using registerDomainFactory().
   * 
   * @example
   * ```typescript
   * // This is called automatically in the constructor
   * this.registerDomainFactories();
   * ```
   */
  private registerDomainFactories(): void {
    this.domainFactories.set('authorization', this.authorizationDomainFactory);
    this.domainFactories.set('ecommerce', this.ecommerceDomainFactory);
    this.logger.log('Registered default domain factories: authorization, ecommerce');
  }

  /**
   * Creates a unified ability for a user that includes all domain permissions.
   * 
   * This is the main method for creating comprehensive abilities that can handle
   * permissions across all domains in the application. It combines rules from
   * all registered domain factories and applies global restrictions.
   * 
   * Process:
   * 1. Creates a CASL AbilityBuilder for unified subjects
   * 2. Applies rules from all registered domain factories
   * 3. Applies global restrictions that apply across all domains
   * 4. Builds and returns the unified ability
   * 
   * @param profile - The user profile containing roles and permissions
   * @returns UnifiedAbility - The unified ability for the user
   * 
   * @example
   * ```typescript
   * const factory = new UnifiedAbilityFactory(logger, i18n, authFactory, ecommerceFactory);
   * const ability = factory.createForUser(userProfile);
   * 
   * // Check permissions across all domains
   * const canManageProducts = ability.can('manage', 'Product');
   * const canReadProfiles = ability.can('read', 'Profile');
   * const canManageAll = ability.can('manage', 'all');
   * ```
   */
  createForUser(profile: Profile): UnifiedAbility {
    const { can, cannot, build } = new AbilityBuilder<UnifiedAbility>(
      PureAbility as AbilityClass<UnifiedAbility>,
    );

    // Apply rules from all domain factories
    this.applyAllDomainRules(profile, { can, cannot });

    // Apply global restrictions
    this.applyGlobalRestrictions(profile, { can, cannot });

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<UnifiedSubjects>,
    });
  }

  /**
   * Applies rules from all registered domain factories.
   * 
   * This method iterates through all registered domain factories and applies
   * their specific rules to the unified ability builder. Each domain factory
   * contributes its domain-specific permissions to the overall ability.
   * 
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   * 
   * @example
   * ```typescript
   * // This is called internally by createForUser()
   * this.applyAllDomainRules(profile, { can, cannot });
   * ```
   */
  private applyAllDomainRules(profile: Profile, abilityBuilder: any): void {
    for (const [domainName, factory] of this.domainFactories) {
      try {
        const domainAbility = factory.createForUser(profile);
        const builtAbility = domainAbility.build();

        // Extract rules from the built ability and apply them to the unified ability
        this.transferRules(builtAbility, abilityBuilder);

        this.logger.log(`Applied ${domainName} domain rules for user ${profile.id}`);
      } catch (error) {
        this.logger.error(`Failed to apply ${domainName} domain rules: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * Transfers rules from a domain ability to the unified ability builder.
   * 
   * This method extracts the rules defined in a domain-specific ability
   * and applies them to the unified ability builder. This ensures that
   * all domain-specific permissions are included in the final unified ability.
   * 
   * Note: This is a simplified implementation. In a production environment,
   * you might need more sophisticated rule extraction and transfer logic.
   * 
   * @param sourceAbility - The source domain ability
   * @param targetBuilder - The target unified ability builder
   * 
   * @example
   * ```typescript
   * // This is called internally by applyAllDomainRules()
   * this.transferRules(builtAbility, abilityBuilder);
   * ```
   */
  private transferRules(sourceAbility: any, targetBuilder: any): void {
    // This is a simplified transfer - in a real implementation, you might need
    // to extract rules from the CASL ability and reapply them
    // For now, we'll rely on the domain factories to apply rules directly
  }

  /**
   * Applies global restrictions that apply across all domains.
   * 
   * These are system-wide restrictions that ensure security and data integrity
   * regardless of the specific domain. They are applied after domain-specific
   * rules to ensure they take precedence.
   * 
   * Global Restrictions:
   * - Prevent deletion of critical system entities
   * - Restrict access to staff-only entities for customers
   * - Apply rate limiting for certain actions
   * - Ensure audit trail requirements
   * 
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   * 
   * @example
   * ```typescript
   * // This is called internally by createForUser()
   * this.applyGlobalRestrictions(profile, { can, cannot });
   * ```
   */
  private applyGlobalRestrictions(profile: Profile, abilityBuilder: any): void {
    // Global restrictions that apply across all domains

    // Prevent deletion of critical system entities
    abilityBuilder.cannot(UserActions.DELETE, Profile).because(
      this.i18n.t('errors.cannot_delete_critical', { args: { entity: 'profiles' } })
    );

    abilityBuilder.cannot(UserActions.DELETE, Role).because(
      this.i18n.t('errors.cannot_delete_critical', { args: { entity: 'roles' } })
    );

    // Prevent customers from accessing staff-only entities
    if (profile.profileType !== 'STAFF') {
      abilityBuilder.cannot(UserActions.MANAGE, Staff);
      abilityBuilder.cannot(UserActions.MANAGE, Permission);
    }

    // Apply rate limiting for certain actions
    this.applyRateLimiting(profile, abilityBuilder);
  }

  /**
   * Applies rate limiting for certain actions.
   * 
   * This method implements rate limiting logic for actions that should be
   * restricted based on frequency or other criteria. In a production environment,
   * this would typically integrate with a rate limiting service.
   * 
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   * 
   * @example
   * ```typescript
   * // This is called internally by applyGlobalRestrictions()
   * this.applyRateLimiting(profile, abilityBuilder);
   * ```
   */
  private applyRateLimiting(profile: Profile, abilityBuilder: any): void {
    // Example: Limit review creation for customers
    if (profile.profileType !== 'STAFF') {
      // This would typically integrate with a rate limiting service
      // For now, we'll just log the intention
      this.logger.log(`Rate limiting applied for user ${profile.id}`);
    }
  }

  /**
   * Gets a domain-specific ability for a user.
   * 
   * This method provides access to domain-specific abilities, allowing you
   * to work with permissions for a specific domain rather than the entire
   * application. This is useful for domain-specific operations or when
   * you need to isolate permission logic.
   * 
   * @param profile - The user profile
   * @param domainName - Name of the domain (e.g., 'authorization', 'ecommerce')
   * @returns any - The domain-specific ability
   * @throws Error - If the domain factory is not found
   * 
   * @example
   * ```typescript
   * const authAbility = factory.getDomainAbility(profile, 'authorization');
   * const ecommerceAbility = factory.getDomainAbility(profile, 'ecommerce');
   * 
   * // Use domain-specific abilities
   * const canManageRoles = authAbility.build().can('manage', 'Role');
   * const canCreateProduct = ecommerceAbility.build().can('create', 'Product');
   * ```
   */
  getDomainAbility(profile: Profile, domainName: string): any {
    const factory = this.domainFactories.get(domainName);
    if (!factory) {
      throw new Error(`Domain factory not found: ${domainName}`);
    }
    return factory.createForUser(profile);
  }

  /**
   * Checks if a user has permission for a specific domain.
   * 
   * This method provides a convenient way to check permissions within a
   * specific domain without needing to work with the full unified ability.
   * It's useful for domain-specific permission checks.
   * 
   * @param profile - The user profile
   * @param domainName - Name of the domain
   * @param action - Action to check
   * @param subject - Subject to check against
   * @returns boolean - True if the user has the permission, false otherwise
   * 
   * @example
   * ```typescript
   * const canManageProducts = factory.hasDomainPermission(
   *   profile, 'ecommerce', UserActions.MANAGE, 'Product'
   * );
   * 
   * const canReadProfiles = factory.hasDomainPermission(
   *   profile, 'authorization', UserActions.READ, 'Profile'
   * );
   * ```
   */
  hasDomainPermission(profile: Profile, domainName: string, action: UserActions, subject: any): boolean {
    try {
      const domainAbility = this.getDomainAbility(profile, domainName);
      const builtAbility = domainAbility.build();
      return builtAbility.can(action, subject);
    } catch (error) {
      this.logger.error(`Error checking domain permission: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Gets all available domains.
   * 
   * This method returns a list of all registered domain names, which is
   * useful for debugging, administration, or dynamic domain discovery.
   * 
   * @returns string[] - Array of available domain names
   * 
   * @example
   * ```typescript
   * const domains = factory.getAvailableDomains();
   * console.log('Available domains:', domains); // ['authorization', 'ecommerce']
   * ```
   */
  getAvailableDomains(): string[] {
    return Array.from(this.domainFactories.keys());
  }

  /**
   * Registers a new domain factory.
   * 
   * This method allows dynamic registration of new domain factories,
   * enabling the system to be extended with new domains without
   * modifying the core factory code.
   * 
   * @param domainName - Name of the domain
   * @param factory - Domain ability factory to register
   * 
   * @example
   * ```typescript
   * const inventoryFactory = new InventoryDomainAbilityFactory(logger, i18n);
   * factory.registerDomainFactory('inventory', inventoryFactory);
   * 
   * // Now the inventory domain is available
   * const inventoryAbility = factory.getDomainAbility(profile, 'inventory');
   * ```
   */
  registerDomainFactory(domainName: string, factory: DomainAbilityFactory): void {
    this.domainFactories.set(domainName, factory);
    this.logger.log(`Registered domain factory: ${domainName}`);
  }
} 