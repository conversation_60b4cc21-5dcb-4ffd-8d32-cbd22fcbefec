import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { Profile } from '@core/profile/entities/profile.entity';
import { AbstractEntity } from '@common/base.entity';

/**
 * Staff entity - Represents a staff member in the system.
 */
@Entity({ name: 'staff' })
export class Staff extends AbstractEntity {
  @AutoMap(() => Profile)
  @OneToOne(() => Profile, { cascade: true, eager: true })
  @JoinColumn({ name: 'profile_id', referencedColumnName: 'id' })
  profile: Profile;
}
