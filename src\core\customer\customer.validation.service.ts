import { Injectable, NotFoundException } from '@nestjs/common';
import { Customer } from './entities/customer.entity';
import { LoggerService } from '@common/logger/logger.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class CustomerValidationService implements ValidationStrategy<Customer> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Customer) private readonly customerRepository: Repository<Customer>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(CustomerValidationService.name);
  }
  async validate(data: Customer, action: DatabaseAction) {
    const existingCustomer = await this.customerRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
    }

    if (action === DatabaseAction.UPDATE && !existingCustomer) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Customer' } }));
    }
  }
}
