import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { CartItem } from '@core/cart_item/entities/cart_item.entity';
import { AutoMap } from '@automapper/classes';
import { Customer } from '@core/customer/entities/customer.entity';
import { AbstractEntity } from '@common/base.entity';

/**
 * Cart entity representing a shopping cart.
 * It contains a reference to the customer who owns the cart and a list of cart items.
 */
@Entity({ name: 'cart' })
export class Cart extends AbstractEntity{

  @OneToOne(() => Customer, { eager: true })
  @JoinColumn({ name: 'customer_id', referencedColumnName: 'id' })
  customer: Customer; // Reference to the customer who owns the cart

  @OneToMany(() => CartItem, (item) => item.cart, {
    cascade: true,
    eager: true,
    orphanedRowAction: 'delete',
  })
  items: CartItem[];
}
