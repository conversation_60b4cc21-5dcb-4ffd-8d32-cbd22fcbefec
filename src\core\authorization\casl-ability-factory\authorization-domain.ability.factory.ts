import { Injectable } from '@nestjs/common';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { Role } from '@core/authorization/role/entities/role.entity';
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { BaseDomainAbilityFactory } from './base-domain-ability.factory';
import { DomainAbility } from './domain-ability.interface';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { ProfileType } from '@common/enumerations/profile_type.enum';

/**
 * Domain-specific ability factory for authorization-related entities.
 *
 * This factory handles permissions for the authorization domain, which includes
 * Profile, Role, and Permission entities. It implements business rules specific
 * to user management, role assignment, and permission control.
 *
 * Key Features:
 * - Different permission sets for STAFF vs CUSTOMER profiles
 * - Field-level restrictions for sensitive data
 * - Role-based access control
 * - Permission inheritance and delegation
 *
 * Business Rules:
 * - STAFF profiles have broader permissions than CUSTOMER profiles
 * - Certain critical operations are restricted based on profile type
 * - Field-level access control for sensitive profile information
 * - Role and permission management restrictions
 *
 * @example
 * ```typescript
 * const factory = new AuthorizationDomainAbilityFactory(logger, i18n);
 * const ability = factory.createForUser(staffProfile);
 * const canManageRoles = ability.build().can('manage', 'Role');
 * ```
 */
@Injectable()
export class AuthorizationDomainAbilityFactory extends BaseDomainAbilityFactory {
  /**
   * Creates a new AuthorizationDomainAbilityFactory instance.
   *
   * @param logger - Logger service for debugging and monitoring
   * @param i18n - Internationalization service for error messages
   */
  constructor(logger: LoggerService, i18n: I18nService) {
    super(logger, i18n);
  }

  /**
   * Returns the domain name for authorization.
   *
   * @returns string - The authorization domain name
   */
  getDomainName(): string {
    return 'authorization';
  }

  /**
   * Returns the subject classes for the authorization domain.
   *
   * These are the entity classes that belong to the authorization domain:
   * - Profile: User profile management
   * - Role: Role-based access control
   * - Permission: Permission management
   *
   * @returns any[] - Array of authorization domain subject classes
   */
  getSubjects(): any[] {
    return [Profile, Role, Permission];
  }

  /**
   * Returns the subject types for the authorization domain.
   *
   * These should match the subjects returned by getSubjects() and are used
   * for CASL's subject type detection.
   *
   * @returns any[] - Array of authorization domain subject types
   */
  getSubjectTypes(): any[] {
    return [Profile, Role, Permission];
  }

  /**
   * Applies authorization domain-specific permission rules.
   *
   * This method implements the business logic for authorization permissions:
   *
   * STAFF Profile Rules:
   * - Can manage all profiles (read, update, create)
   * - Can manage roles and permissions
   * - Cannot delete critical system entities
   * - Field-level restrictions on sensitive data
   *
   * CUSTOMER Profile Rules:
   * - Can read and update their own profile
   * - Cannot manage roles or permissions
   * - Cannot delete profiles
   * - Limited field access
   *
   * @param profile - The user profile to apply rules for
   * @param ability - The domain ability to configure with rules
   *
   * @example
   * ```typescript
   * // STAFF can manage all profiles but with field restrictions
   * if (profile.profileType === 'STAFF') {
   *   ability.can(UserActions.MANAGE, 'Profile');
   *   ability.cannot(UserActions.UPDATE, 'Profile', 'password');
   * }
   *
   * // CUSTOMER can only manage their own profile
   * if (profile.profileType === 'CUSTOMER') {
   *   ability.can(UserActions.READ, 'Profile', undefined, { id: profile.id });
   *   ability.can(UserActions.UPDATE, 'Profile', undefined, { id: profile.id });
   * }
   * ```
   */
  protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
    this.logger.log(`Applying authorization domain rules for user ${profile.id} (${profile.profileType})`);

    // Apply permission-based rules using the user's actual role permissions
    this.applyPermissionBasedRules(profile, ability);

    // Apply common restrictions
    this.applyCommonRestrictions(profile, ability);
  }

  /**
   * Applies permission-based rules using the user's actual role permissions.
   *
   * This method creates CASL abilities based on the user's actual permissions
   * stored in their role, rather than hardcoded role-based rules. This provides
   * true permission-based access control.
   *
   * @param profile - The user profile with role and permissions
   * @param ability - The domain ability to configure
   */
  private applyPermissionBasedRules(profile: Profile, ability: DomainAbility): void {
    if (!profile.role || !profile.role.permissions) {
      this.logger.warn(`No role or permissions found for user ${profile.id}`);
      return;
    }

    // Apply permissions based on the user's actual role permissions
    for (const permission of profile.role.permissions) {
      const permissionName = permission.name.toLowerCase();
      
      // Map permission names to CASL actions and subjects
      this.mapPermissionToAbility(permissionName, ability, profile);
    }

    this.logger.log(`Applied permission-based rules for user ${profile.id} with ${profile.role.permissions.length} permissions`);
  }

  /**
   * Maps permission names to CASL ability rules.
   *
   * This method translates permission names from the database into
   * CASL ability rules. It supports various permission naming conventions
   * and maps them to appropriate actions and subjects.
   *
   * @param permissionName - The permission name from the database
   * @param ability - The domain ability to configure
   * @param profile - The user profile for context
   */
  private mapPermissionToAbility(permissionName: string, ability: DomainAbility, profile: Profile): void {
    // Parse permission name (e.g., "profile:read", "role:manage", "permission:create")
    const parts = permissionName.split(':');
    if (parts.length !== 2) {
      this.logger.warn(`Invalid permission format: ${permissionName}`);
      return;
    }

    const [subject, action] = parts;
    const subjectName = subject.charAt(0).toUpperCase() + subject.slice(1); // Capitalize first letter

    // Map action to UserActions enum
    let userAction: UserActions;
    switch (action.toLowerCase()) {
      case 'read':
        userAction = UserActions.READ;
        break;
      case 'create':
        userAction = UserActions.CREATE;
        break;
      case 'update':
        userAction = UserActions.UPDATE;
        break;
      case 'delete':
        userAction = UserActions.DELETE;
        break;
      case 'manage':
        userAction = UserActions.MANAGE;
        break;
      default:
        this.logger.warn(`Unknown action: ${action} in permission: ${permissionName}`);
        return;
    }

    // Apply the permission
    if (subjectName === 'Profile' && userAction !== UserActions.MANAGE) {
      // For profile permissions, add conditions to restrict to own profile unless it's manage
      if (userAction === UserActions.READ || userAction === UserActions.UPDATE) {
        ability.can(userAction, 'Profile', undefined, { id: profile.id });
      } else {
        ability.can(userAction, 'Profile');
      }
    } else {
      ability.can(userAction, subjectName);
    }
  }

  /**
   * Applies common restrictions that apply to all profile types.
   *
   * These are global restrictions that ensure system security and data integrity:
   * - Prevent deletion of critical system entities
   * - Apply rate limiting for certain operations
   * - Ensure audit trail requirements
   *
   * @param profile - The user profile
   * @param ability - The domain ability to configure
   */
  private applyCommonRestrictions(profile: Profile, ability: DomainAbility): void {
    // Prevent deletion of critical system entities
    ability.cannot(UserActions.DELETE, 'Profile');
    ability.cannot(UserActions.DELETE, 'Role');
    ability.cannot(UserActions.DELETE, 'Permission');

    // Prevent modification of system-critical fields
    ability.cannot(UserActions.UPDATE, 'Profile', 'id');
    ability.cannot(UserActions.UPDATE, 'Role', 'id');
    ability.cannot(UserActions.UPDATE, 'Permission', 'id');

    this.logger.log(`Applied common restrictions for user ${profile.id}`);
  }

  /**
   * Gets field-level restrictions for authorization domain entities.
   *
   * Provides fine-grained control over which fields users can access
   * based on their profile type and permissions.
   *
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns FieldRestrictions - Object mapping field names to boolean restrictions
   */
  protected getFieldRestrictions(profile: Profile, subject: string): any {
    if (subject === 'Profile') {
      if (profile.profileType === ProfileType.CLIENT) {
        return {
          id: false,           // Cannot access ID
          role: false,         // Cannot access role
          profileType: false,  // Cannot access profile type
          password: false,     // Cannot access password
          email: true,         // Can access email
          name: true,          // Can access name
          phone: true          // Can access phone
        };
      }
    }

    return {};
  }

  /**
   * Gets conditions for authorization domain entities.
   *
   * Provides conditions that restrict access based on user ownership
   * and business rules.
   *
   * @param profile - The user profile
   * @param subject - The subject/entity name
   * @returns AbilityConditions - Object containing conditions for permission checks
   */
  protected getConditions(profile: Profile, subject: string): any {
    if (subject === 'Profile' && profile.profileType === ProfileType.CLIENT) {
      return {
        id: profile.id  // Can only access own profile
      };
    }

    return {};
  }
}
