import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Post, Req, Res, UseGuards } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { ApiBody, ApiExcludeEndpoint, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { I18nService } from 'nestjs-i18n';
import { AuthenticationService } from '@core/authentication/authentication.service';
import { SignInDto } from '@core/authentication/dto/sign-in.dto';
import { ForgotPasswordDto } from '@core/authentication/dto/forgot-password.dto';
import { CreateProfileDto } from '@core/profile/dto/create-profile.dto';
import { VerifyAccountDto } from './dto/verify-account.dto';
import { ResendVerificationOtpDto } from './dto/resend-verification-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileDto } from '@core/profile/dto/profile.dto';
import { RefreshTokenGuard } from './guard/refresh-token.guard';
import { JwtAuthGuard } from './guard/jwt-auth.guard';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';

@ApiTags('Authentication Endpoints')
@Controller({
  path: 'auth',
  version: '1',
})
export class AuthenticationController {
  constructor(
    private readonly logger: LoggerService,
    private readonly authService: AuthenticationService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(AuthenticationController.name);
  }

  @Public()
  @ApiOperation({ summary: 'User Registration' })
  @ApiBody({ type: CreateProfileDto })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @ApiHeader({ name: 'x-global-user', description: 'Whether the user is a global user', required: false })
  @HttpCode(HttpStatus.CREATED)
  @Post('signup')
  async signup(@Body() createProfileDto: CreateProfileDto, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;
      const isGlobalUser = headers['x-global-user'] === 'true';

      const profile = this.classMapper.map(createProfileDto, CreateProfileDto, Profile);

      // Set tenant context if provided
      if (tenantId && !isGlobalUser) {
        profile.tenantId = tenantId;
      }

      await this.authService.signup(profile);
      return {
        message: this.i18n.t('success.registration'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'User Sign-In' })
  @ApiBody({ type: SignInDto })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @ApiHeader({ name: 'x-global-user', description: 'Whether the user is a global user', required: false })
  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(@Body() loginDto: SignInDto, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const { email, password } = loginDto;
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;
      const isGlobalUser = headers['x-global-user'] === 'true';

      const result = await this.authService.signin(email, password, tenantId, isGlobalUser);
      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        profileInfo: this.classMapper.map(result.profile, Profile, ProfileDto),
        tenantId: result.profile.tenantId,
        isGlobalUser,
      };
      return { message: this.i18n.t('success.login'), data };
    });
  }

  @Public()
  @UseGuards(RefreshTokenGuard)
  @ApiOperation({ summary: 'Refresh Access Token' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @ApiHeader({ name: 'x-global-user', description: 'Whether the user is a global user', required: false })
  @HttpCode(HttpStatus.OK)
  @Post('refresh')
  async refreshTokens(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const { userId, email, refreshToken, tenantId, isGlobalUser } = req.user;
      const headerTenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;
      const headerIsGlobalUser = headers['x-global-user'] === 'true';

      // Use token tenant context if available, otherwise use header context
      const finalTenantId = tenantId || headerTenantId;
      const finalIsGlobalUser = isGlobalUser || headerIsGlobalUser;

      const result = await this.authService.refreshTokens(
        userId,
        email,
        refreshToken,
        finalTenantId,
        finalIsGlobalUser
      );

      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        tenantId: finalTenantId,
        isGlobalUser: finalIsGlobalUser,
      };
      return { message: this.i18n.t('success.tokens_refreshed'), data };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'User Logout' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @HttpCode(HttpStatus.OK)
  @Post('logout')
  async logout(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const refreshToken = req.headers.authorization?.replace('Bearer ', '');
      const userId = req.user?.id;
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;

      if (refreshToken) {
        await this.authService.logout(refreshToken, userId, tenantId);
      }
      return {
        message: this.i18n.t('success.logout'),
        data: null,
      };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Logout from All Devices' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @HttpCode(HttpStatus.OK)
  @Post('logout-all-devices')
  async logoutAllDevices(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const userId = req.user.id;
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;

      await this.authService.logoutAllDevices(userId, tenantId);
      return {
        message: this.i18n.t('success.logout_all_devices'),
        data: null,
      };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get Active Sessions' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @HttpCode(HttpStatus.OK)
  @Get('sessions')
  async getActiveSessions(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const userId = req.user.id;
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;

      const sessions = await this.authService.getActiveSessions(userId, tenantId);
      return {
        message: this.i18n.t('success.sessions_retrieved'),
        data: { sessions },
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Verify Account' })
  @HttpCode(HttpStatus.OK)
  @Post('verify-account')
  async verifyAccount(@Body() dto: VerifyAccountDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, otp } = dto;
      await this.authService.verifyProfile(email, otp);

      return {
        message: this.i18n.t('success.account_verified'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Resend Verification OTP' })
  @HttpCode(HttpStatus.OK)
  @Post('resend-verification-otp')
  async resendOtp(@Body() dto: ResendVerificationOtpDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.resendVerificationOtp(dto.email);
      return {
        message: this.i18n.t('success.verification_otp_resent'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Forgot password' })
  @HttpCode(HttpStatus.OK)
  @Post('forgot-password')
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.forgotPassword(dto.email);
      return {
        message: this.i18n.t('success.password_reset_otp_sent'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Reset password' })
  @HttpCode(HttpStatus.OK)
  @Post('reset-password')
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, otp, newPassword } = dto;
      await this.authService.resetPassword(email, otp, newPassword);
      return {
        message: this.i18n.t('success.reset_password'),
        data: null,
      };
    });
  }

  @ApiExcludeEndpoint()
  @Public()
  @ApiOperation({ summary: 'Initiate Google OAuth Sign In/Sign Up' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @ApiHeader({ name: 'x-global-user', description: 'Whether the user is a global user', required: false })
  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // This endpoint initiates Google OAuth flow
    // The guard will redirect to Google
  }

  @ApiExcludeEndpoint()
  @Public()
  @ApiOperation({ summary: 'Google OAuth Callback' })
  @ApiHeader({ name: 'x-tenant-id', description: 'Tenant ID for multi-tenant isolation', required: false })
  @ApiHeader({ name: 'x-global-user', description: 'Whether the user is a global user', required: false })
  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthCallback(@Req() req: any, @Res() res: Response, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const googleProfile = req.user;
      const tenantId = headers['x-tenant-id'] ? parseInt(headers['x-tenant-id']) : undefined;
      const isGlobalUser = headers['x-global-user'] === 'true';

      const result = await this.authService.handleGoogleAuth(googleProfile, tenantId, isGlobalUser);

      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        profileInfo: this.classMapper.map(result.profile, Profile, ProfileDto),
        tenantId: result.profile.tenantId,
        isGlobalUser,
        isNewUser: result.isNewUser
      };

      // For frontend applications, you might want to redirect to a specific URL
      // with the tokens as query parameters or use a different approach
      const redirectUrl = process.env.GOOGLE_OAUTH_REDIRECT_URL || 'http://localhost:3000/auth/callback';
      const queryParams = new URLSearchParams({
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        isNewUser: result.isNewUser.toString(),
        email: result.profile.email
      });

      res.redirect(`${redirectUrl}?${queryParams.toString()}`);

      return {
        message: result.isNewUser
          ? this.i18n.t('success.google_signup')
          : this.i18n.t('success.google_signin'),
        data
      };
    });
  }
}
