import { LoggerService } from '@common/logger/logger.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Cache } from 'cache-manager';

@Injectable()
export class RedisService {
  constructor(
    private readonly logger: LoggerService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.logger.setContext(RedisService.name);
  }

  async getCacheKey(key: string): Promise<any> {
    try {
      return await this.cacheManager.get(key);
    } catch (error) {
      this.logger.error(`Error getting cache key: ${error.message}`);
      // Return null instead of throwing error for cache misses
      return null;
    }
  }

  async setCacheKey(
    key: string,
    value: string,
    ttl = 60 * 1000,
  ): Promise<void> {
    try {
      if (!key) throw new NotFoundException('Key not provided');
      await this.cacheManager.set(key, value, ttl);
    } catch (error) {
      this.logger.error(`Error setting cache key: ${error.message}`);
      throw error;
    }
  }

  async delCacheKey(key: string): Promise<void> {
    if (!key) throw new NotFoundException('Key not found');
    try {
      await this.cacheManager.del(key);
    } catch (error) {
      this.logger.error(`Error deleting cache key: ${error.message}`);
      throw error;
    }
  }

  async isKeyExists(key: string): Promise<boolean> {
    const data = await this.cacheManager.get(key);
    return !!data;
  }
}
