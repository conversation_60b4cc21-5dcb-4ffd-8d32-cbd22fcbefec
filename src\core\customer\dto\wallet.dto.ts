import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';

export class WalletDto {
  @AutoMap()
  @ApiProperty({
    description: 'The balance of the wallet',
    example: 100.50,
    type: Number,
    name: 'balance',
  })
  balance: number;

  @AutoMap()
  @ApiProperty({
    description: 'The currency of the wallet',
    example: 'USD',
    type: String,
    name: 'currency',
  })
  currency: string;
}
