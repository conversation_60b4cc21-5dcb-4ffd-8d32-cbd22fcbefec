import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';
import { Staff } from './entities/staff.entity';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class StaffValidationService implements ValidationStrategy<Staff> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Staff) private readonly staffRepository: Repository<Staff>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(StaffValidationService.name);
  }
  async validate(data: Staff, action: DatabaseAction) {
    const existingStaff = await this.staffRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
    }

    if (action === DatabaseAction.UPDATE && !existingStaff) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Staff' } }));
    }
  }
}
