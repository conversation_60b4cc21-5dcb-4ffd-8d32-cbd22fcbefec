# Authentication System Documentation

## Overview

The authentication system provides secure user authentication with JWT tokens, featuring access tokens (15 minutes) and refresh tokens (7 days). The system includes comprehensive security features, token management, and user session handling.

## Token Configuration

### Current Token Settings

```typescript
// Access Token: 15 minutes
expiresIn: '15m'

// Refresh Token: 7 days  
expiresIn: '7d'
```

### Token Structure

#### Access Token Payload
```typescript
{
  sub: userId,    // User ID
  email: string,  // User email
  iat: number,    // Issued at timestamp
  exp: number     // Expiration timestamp
}
```

#### Refresh Token Payload
```typescript
{
  sub: userId,    // User ID
  email: string,  // User email
  iat: number,    // Issued at timestamp
  exp: number     // Expiration timestamp (7 days)
}
```

## Authentication Flow

### 1. User Registration
```typescript
POST /api/v1/auth/signup
{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+2347012345678",
  "password": "securePassword123"
}
```

### 2. User Login
```typescript
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

// Response
{
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "profileInfo": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      // ... other profile fields
    }
  }
}
```

### 3. Token Refresh
```typescript
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>

// Response
{
  "message": "Tokens refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 4. User Logout
```typescript
POST /api/v1/auth/logout
Authorization: Bearer <access_token>

// Response
{
  "message": "Logout successful",
  "data": null
}
```

### 5. Logout from All Devices
```typescript
POST /api/v1/auth/logout-all-devices
Authorization: Bearer <access_token>

// Response
{
  "message": "Logged out from all devices",
  "data": null
}
```

## Security Features

### 1. Token Blacklisting
- Refresh tokens are blacklisted in Redis when used or revoked
- Blacklisted tokens are stored for 7 days (matching token expiration)
- Prevents reuse of compromised tokens

### 2. Failed Login Protection
- Tracks failed login attempts
- Suspends account after maximum failed attempts
- Resets failed count on successful login

### 3. Profile Eligibility Checks
- Verifies account is active before allowing access
- Checks for suspended/banned accounts
- Validates profile status on every request

### 4. Password Security
- Passwords are hashed using bcrypt
- Password reset functionality with OTP
- Secure password validation

## API Endpoints

### Public Endpoints (No Authentication Required)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/auth/signup` | POST | User registration |
| `/api/v1/auth/login` | POST | User login |
| `/api/v1/auth/refresh` | POST | Refresh access token |
| `/api/v1/auth/verify-account` | POST | Verify account with OTP |
| `/api/v1/auth/resend-verification-otp` | POST | Resend verification OTP |
| `/api/v1/auth/forgot-password` | POST | Request password reset |
| `/api/v1/auth/reset-password` | POST | Reset password with OTP |

### Protected Endpoints (Authentication Required)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/auth/logout` | POST | User logout |
| `/api/v1/auth/logout-all-devices` | POST | Logout from all devices |

## Token Management

### Access Token Usage
```typescript
// Include in API requests
Authorization: Bearer <access_token>

// Example
fetch('/api/v1/profile', {
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  }
})
```

### Refresh Token Usage
```typescript
// Use when access token expires
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

### Token Expiration Handling
```typescript
// Client-side token refresh logic
async function refreshTokens() {
  try {
    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${refreshToken}`
      }
    });
    
    const { accessToken, refreshToken } = await response.json();
    
    // Update stored tokens
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    
    return accessToken;
  } catch (error) {
    // Redirect to login
    window.location.href = '/login';
  }
}
```

## Error Handling

### Common Error Responses

#### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}
```

#### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "Account is suspended",
  "error": "Forbidden"
}
```

#### 429 Too Many Requests
```json
{
  "statusCode": 429,
  "message": "Too many failed login attempts",
  "error": "Too Many Requests"
}
```

## Redis Integration

### Token Blacklisting
```typescript
// Blacklist a refresh token
await redisService.set(`blacklist:${refreshToken}`, 'revoked', 7 * 24 * 60 * 60);

// Check if token is blacklisted
const isBlacklisted = await redisService.get(`blacklist:${refreshToken}`);
if (isBlacklisted) {
  throw new ForbiddenException('Token has been revoked');
}
```

### Session Management
```typescript
// Store active sessions per user
await redisService.set(`sessions:${userId}`, JSON.stringify(sessions), 7 * 24 * 60 * 60);

// Get user sessions
const sessions = await redisService.get(`sessions:${userId}`);
```

## Configuration

### Environment Variables
```env
# JWT Secret Key
SECRET=your-super-secret-jwt-key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
NODE_ENV=development
PORT=3080
```

### JWT Configuration
```typescript
// Authentication Module
JwtModule.registerAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    secret: configService.get<string>('keys.secret'),
    // Token expiration is set manually in createTokens method
  }),
  inject: [ConfigService],
})
```

## Security Best Practices

### 1. Token Storage
- **Access Token**: Store in memory (not localStorage)
- **Refresh Token**: Store in httpOnly cookie or secure storage
- **Never store tokens in localStorage** (vulnerable to XSS)

### 2. Token Rotation
- Refresh tokens are rotated on each use
- Old refresh tokens are blacklisted
- Prevents token reuse attacks

### 3. Secure Headers
```typescript
// Include in responses
res.setHeader('X-Content-Type-Options', 'nosniff');
res.setHeader('X-Frame-Options', 'DENY');
res.setHeader('X-XSS-Protection', '1; mode=block');
```

### 4. Rate Limiting
- Implement rate limiting on authentication endpoints
- Prevent brute force attacks
- Use Redis for distributed rate limiting

## Testing

### Unit Tests
```typescript
describe('AuthenticationService', () => {
  it('should create tokens with correct expiration', async () => {
    const tokens = await authService.createTokens(1, '<EMAIL>');
    
    // Verify access token expiration (15 minutes)
    const accessTokenPayload = jwt.decode(tokens.accessToken);
    const accessTokenExp = accessTokenPayload.exp;
    const now = Math.floor(Date.now() / 1000);
    expect(accessTokenExp - now).toBeCloseTo(15 * 60, -1);
    
    // Verify refresh token expiration (7 days)
    const refreshTokenPayload = jwt.decode(tokens.refreshToken);
    const refreshTokenExp = refreshTokenPayload.exp;
    expect(refreshTokenExp - now).toBeCloseTo(7 * 24 * 60 * 60, -1);
  });
});
```

### Integration Tests
```typescript
describe('Authentication Endpoints', () => {
  it('should login and return tokens', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expect(response.status).toBe(200);
    expect(response.body.data).toHaveProperty('accessToken');
    expect(response.body.data).toHaveProperty('refreshToken');
  });
});
```

## Monitoring and Logging

### Authentication Logs
```typescript
// Successful login
logger.log(`User ${email} logged in successfully`);

// Failed login attempt
logger.warn(`Failed login attempt for user ${email}`);

// Token refresh
logger.log(`Tokens refreshed for user ${email}`);

// Logout
logger.log(`User ${email} logged out`);
```

### Security Alerts
```typescript
// Suspicious activity detection
if (failedAttempts > 5) {
  logger.error(`Multiple failed login attempts for user ${email}`);
  // Send security alert
}
```

## Troubleshooting

### Common Issues

1. **Token Expired**
   - Use refresh token to get new access token
   - Redirect to login if refresh token is expired

2. **Invalid Token**
   - Check token format and signature
   - Verify token hasn't been blacklisted

3. **Account Suspended**
   - Check profile status
   - Contact support for account reactivation

4. **Redis Connection Issues**
   - Check Redis server status
   - Verify Redis configuration
   - Implement fallback mechanisms

### Debug Mode
```typescript
// Enable detailed logging
logger.setLevel('debug');

// Log token details (development only)
logger.debug(`Token payload: ${JSON.stringify(payload)}`);
```

## Performance Considerations

1. **Token Validation**: Use Redis for fast token blacklist checks
2. **Database Queries**: Minimize database calls during authentication
3. **Caching**: Cache user profile data for frequently accessed users
4. **Connection Pooling**: Use connection pooling for database and Redis

## Future Enhancements

1. **Multi-Factor Authentication**: Add 2FA support
2. **OAuth Integration**: Google, Facebook, GitHub login
3. **Device Management**: Track and manage active devices
4. **Session Analytics**: Monitor user session patterns
5. **Advanced Security**: Implement device fingerprinting 