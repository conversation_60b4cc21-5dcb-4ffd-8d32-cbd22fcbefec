import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, IsNumber } from 'class-validator';

export class GoogleAuthResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    type: String,
    name: 'accessToken',
  })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    type: String,
    name: 'refreshToken',
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'User profile information',
    type: Object,
    name: 'profileInfo',
  })
  profileInfo: any;

  @ApiProperty({
    description: 'Tenant ID for multi-tenant isolation',
    required: false,
    type: Number,
    name: 'tenantId',
  })
  @IsOptional()
  @IsNumber()
  tenantId?: number;

  @ApiProperty({
    description: 'Whether the user is a global user',
    required: false,
    type: Boolean,
    name: 'isGlobalUser',
  })
  @IsBoolean()
  isGlobalUser: boolean;

  @ApiProperty({
    description: 'Whether this is a new user registration',
    required: false,
    type: Boolean,
    name: 'isNew<PERSON><PERSON>',
  })
  @IsBoolean()
  isNewUser: boolean;
}
