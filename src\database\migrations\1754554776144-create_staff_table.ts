import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateStaffTable1754554776144 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
          new Table({
              name: 'staff',
              columns: [
                  { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
                  { name: 'tenant_id', type: 'bigint', isNullable: true },
                  { name: 'status', type: 'varchar' },
                  { name: 'created_at', type: 'timestamp', default: 'now()' },
                  { name: 'created_by', type: 'varchar' },
                  { name: 'updated_at', type: 'timestamp', default: 'now()' },
                  { name: 'updated_by', type: 'varchar', isNullable: true },
                  { name: 'deleted_at', type: 'timestamp', isNullable: true },
                  { name: 'profile_id', type: 'bigint', isNullable: true },
              ]
          }),
          true
        )

        await queryRunner.createIndex(
          'staff',
          new TableIndex({
              name: 'IDX_STAFF_FIELDS',
              columnNames: [
                  'id',
                  'status',
                  'created_at',
                  'updated_at',
              ],
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropIndex('staff', 'IDX_STAFF_FIELDS');
        await queryRunner.dropTable('staff');
    }

}
