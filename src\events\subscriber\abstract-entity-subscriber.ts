import { EntitySubscriberInterface, EventSubscriber, InsertEvent, RemoveEvent, UpdateEvent } from "typeorm";
import { AbstractEntity } from '@common/base.entity';
import { LoggerService } from '@common/logger/logger.service';

@EventSubscriber()
export class AbstractEntitySubscriber implements EntitySubscriberInterface<AbstractEntity> {
  private readonly logger = new LoggerService(AbstractEntitySubscriber.name);

  /**
   * Listen to all entities that extend AbstractEntity.
   */
  listenTo() {
    return AbstractEntity;
  }

  /**
   * Before an entity is inserted.
   * Sets createdBy, updatedBy, and createdAt fields.
   */
  beforeInsert(event: InsertEvent<AbstractEntity>): void {
    try {
      const currentUser = AbstractEntity.getCurrentUser();
      const entityName = event.entity.constructor.name;
      
      // Set audit fields
      event.entity.createdBy = currentUser;
      event.entity.updatedBy = currentUser;
      event.entity.createdAt = new Date();
      
      this.logger.log(`Setting audit fields for ${entityName} - createdBy: ${currentUser}, createdAt: ${event.entity.createdAt}`);
    } catch (error) {
      this.logger.error(`Error in beforeInsert for ${event.entity.constructor.name}: ${error.message}`, error.stack);
      // Don't throw error to prevent blocking the insert operation
    }
  }

  /**
   * Before an entity is updated.
   * Sets updatedBy and updatedAt fields.
   */
  beforeUpdate(event: UpdateEvent<AbstractEntity>): void {
    try {
      if (!event.entity) {
        this.logger.warn('beforeUpdate called but event.entity is null/undefined');
        return;
      }

      const currentUser = AbstractEntity.getCurrentUser();
      const entityName = event.entity.constructor.name;
      
      // Set audit fields
      event.entity.updatedBy = currentUser;
      event.entity.updatedAt = new Date();
      
      this.logger.log(`Setting audit fields for ${entityName} - updatedBy: ${currentUser}, updatedAt: ${event.entity.updatedAt}`);
    } catch (error) {
      this.logger.error(`Error in beforeUpdate for ${event.entity?.constructor.name || 'unknown'}: ${error.message}`, error.stack);
      // Don't throw error to prevent blocking the update operation
    }
  }

  /**
   * Before an entity is soft-deleted.
   * Sets updatedBy and deletedAt fields.
   */
  beforeRemove(event: RemoveEvent<AbstractEntity>): void {
    try {
      if (!event.entity) {
        this.logger.warn('beforeRemove called but event.entity is null/undefined');
        return;
      }

      const currentUser = AbstractEntity.getCurrentUser();
      const entityName = event.entity.constructor.name;
      
      // Set audit fields
      event.entity.updatedBy = currentUser;
      event.entity.deletedAt = new Date();
      
      this.logger.log(`Setting audit fields for ${entityName} - updatedBy: ${currentUser}, deletedAt: ${event.entity.deletedAt}`);
    } catch (error) {
      this.logger.error(`Error in beforeRemove for ${event.entity?.constructor.name || 'unknown'}: ${error.message}`, error.stack);
      // Don't throw error to prevent blocking the remove operation
    }
  }

  /**
   * After an entity is inserted.
   * Logs successful creation.
   */
  afterInsert(event: InsertEvent<AbstractEntity>): void {
    try {
      const entityName = event.entity.constructor.name;
      const entityId = (event.entity as any).id;
      
      this.logger.log(`Successfully created ${entityName} with ID: ${entityId}`);
    } catch (error) {
      this.logger.error(`Error in afterInsert for ${event.entity.constructor.name}: ${error.message}`, error.stack);
    }
  }

  /**
   * After an entity is updated.
   * Logs successful update.
   */
  afterUpdate(event: UpdateEvent<AbstractEntity>): void {
    try {
      if (!event.entity) {
        this.logger.warn('afterUpdate called but event.entity is null/undefined');
        return;
      }

      const entityName = event.entity.constructor.name;
      const entityId = (event.entity as any).id;
      
      this.logger.log(`Successfully updated ${entityName} with ID: ${entityId}`);
    } catch (error) {
      this.logger.error(`Error in afterUpdate for ${event.entity?.constructor.name || 'unknown'}: ${error.message}`, error.stack);
    }
  }

  /**
   * After an entity is removed.
   * Logs successful removal.
   */
  afterRemove(event: RemoveEvent<AbstractEntity>): void {
    try {
      if (!event.entity) {
        this.logger.warn('afterRemove called but event.entity is null/undefined');
        return;
      }

      const entityName = event.entity.constructor.name;
      const entityId = (event.entity as any).id;
      
      this.logger.log(`Successfully removed ${entityName} with ID: ${entityId}`);
    } catch (error) {
      this.logger.error(`Error in afterRemove for ${event.entity?.constructor.name || 'unknown'}: ${error.message}`, error.stack);
    }
  }
}
