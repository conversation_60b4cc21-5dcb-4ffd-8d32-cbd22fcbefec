import { HttpStatus, Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const tenantId = req.header('x-tenant-id');
    const isGlobalUser = req.header('x-global-user') === 'true';

    if (!tenantId && !isGlobalUser) {
      return res.status(HttpStatus.BAD_REQUEST).json({ message: 'Missing tenant context' });
    }

    req['tenantId'] = tenantId;
    req['isGlobalUser'] = isGlobalUser;
    next();
  }
}
