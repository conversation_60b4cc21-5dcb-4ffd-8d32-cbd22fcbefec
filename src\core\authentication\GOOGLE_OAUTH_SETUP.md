# Google OAuth Setup Guide

This guide explains how to set up and use Google OAuth authentication in your application.

## Prerequisites

1. A Google Cloud Console account
2. A registered application in Google Cloud Console
3. Valid Google OAuth credentials

## Setup Steps

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Configure the OAuth consent screen
6. Create OAuth 2.0 credentials for a "Web application"
7. Add authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback` (for development)
   - `https://yourdomain.com/auth/google/callback` (for production)

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Google OAuth Configuration
GOOGLE_OAUTH_CLIENT_ID=your_google_client_id
GOOGLE_OAUTH_SECRET=your_google_client_secret
GOOGLE_OAUTH_REDIRECT_URL=http://localhost:3000/auth/callback
```

### 3. Frontend Integration

#### Option 1: Direct API Call (Recommended for SPAs)

```javascript
// Redirect to Google OAuth
const initiateGoogleAuth = () => {
  const tenantId = '123'; // Optional
  const isGlobalUser = 'false'; // Optional
  
  const url = `http://localhost:3000/auth/google?x-tenant-id=${tenantId}&x-global-user=${isGlobalUser}`;
  window.location.href = url;
};

// Handle callback
const handleGoogleCallback = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const accessToken = urlParams.get('accessToken');
  const refreshToken = urlParams.get('refreshToken');
  const isNewUser = urlParams.get('isNewUser') === 'true';
  const email = urlParams.get('email');

  if (accessToken && refreshToken) {
    // Store tokens
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    
    // Handle new user onboarding if needed
    if (isNewUser) {
      // Redirect to onboarding or show welcome message
      console.log('New user signed up:', email);
    }
    
    // Redirect to dashboard or home page
    window.location.href = '/dashboard';
  }
};
```

#### Option 2: Google Sign-In Button

```html
<!-- Add Google Sign-In script -->
<script src="https://accounts.google.com/gsi/client" async defer></script>

<!-- Add the button -->
<div id="g_id_onload"
     data-client_id="your_google_client_id"
     data-callback="handleCredentialResponse"
     data-auto_prompt="false">
</div>
<div class="g_id_signin"
     data-type="standard"
     data-size="large"
     data-theme="outline"
     data-text="sign_in_with"
     data-shape="rectangular"
     data-logo_alignment="left">
</div>

<script>
function handleCredentialResponse(response) {
  // Send the ID token to your backend
  fetch('/auth/google/verify-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      idToken: response.credential
    })
  })
  .then(res => res.json())
  .then(data => {
    // Handle successful authentication
    console.log('Authentication successful:', data);
  })
  .catch(error => {
    console.error('Authentication failed:', error);
  });
}
</script>
```

## API Endpoints

### 1. Initiate Google OAuth
```
GET /auth/google
```

**Headers:**
- `x-tenant-id` (optional): Tenant ID for multi-tenant isolation
- `x-global-user` (optional): Whether the user is a global user

**Response:** Redirects to Google OAuth consent screen

### 2. Google OAuth Callback
```
GET /auth/google/callback
```

**Headers:**
- `x-tenant-id` (optional): Tenant ID for multi-tenant isolation
- `x-global-user` (optional): Whether the user is a global user

**Response:** Redirects to frontend with tokens as query parameters

## Features

### Automatic User Creation
- New users are automatically created when they sign in with Google for the first time
- Profile information (name, email, profile picture) is extracted from Google
- Email verification is automatically completed for Google users

### Existing User Sign In
- Existing users can sign in with Google if their email matches
- Profile information is updated with the latest Google data
- Authentication tokens are generated and returned

### Multi-Tenant Support
- Supports tenant-specific authentication
- Global user support for cross-tenant access
- Tenant context is maintained throughout the authentication flow

### Security Features
- JWT tokens for secure authentication
- Refresh token rotation
- Session management with Redis
- Token blacklisting for logout

## Error Handling

The system handles various error scenarios:

1. **Invalid Google Profile**: Missing email or ID
2. **Database Errors**: Profile creation/update failures
3. **Token Generation Errors**: JWT signing failures
4. **Network Errors**: Google API communication issues

## Testing

### Development Testing
1. Set up Google OAuth credentials for localhost
2. Configure redirect URLs for development
3. Test the complete flow from sign-in to callback

### Production Testing
1. Update Google OAuth credentials for production domain
2. Configure production redirect URLs
3. Test with real Google accounts

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error**
   - Ensure the redirect URI in Google Console matches your callback URL
   - Check for trailing slashes or protocol mismatches

2. **"Client ID not found" error**
   - Verify your Google OAuth credentials are correctly set in environment variables
   - Check that the Google+ API is enabled in Google Cloud Console

3. **"Email already exists" error**
   - This is expected behavior for existing users
   - The system will sign in the existing user instead of creating a new one

4. **Token generation failures**
   - Check that your JWT secret is properly configured
   - Verify Redis is running for session storage

### Debug Mode

Enable debug logging by setting the log level to 'debug' in your application configuration.

## Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Token Storage**: Store tokens securely in the frontend
3. **Token Rotation**: Implement automatic token refresh
4. **Session Management**: Implement proper session cleanup
5. **Rate Limiting**: Consider implementing rate limiting for OAuth endpoints

## Best Practices

1. **User Experience**: Provide clear feedback during the OAuth flow
2. **Error Handling**: Implement proper error handling and user feedback
3. **Fallback**: Provide alternative authentication methods
4. **Monitoring**: Monitor OAuth success/failure rates
5. **Testing**: Regularly test the OAuth flow with different scenarios
