import { Injectable } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { Store } from '@core/store/entities/store.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseRepository } from '@common/base.repository';

@Injectable()
export class StoreService implements EntityServiceStrategy<Store> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Store) private readonly storeRepository: BaseRepository<Store>,
  ) {
    this.logger.setContext(StoreService.name);
  }

  activate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  create(data: Store): Promise<Store> {
    return Promise.resolve(undefined);
  }

  deactivate(ids: number[]): Promise<void> {
    return Promise.resolve(undefined);
  }

  findByPk(id: number): Promise<Store | null> {
    return Promise.resolve(undefined);
  }

  modify(id: number, data: Store): Promise<Store> {
    return Promise.resolve(undefined);
  }
}
