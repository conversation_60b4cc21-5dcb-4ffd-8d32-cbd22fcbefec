import { OtpType } from '@common/enumerations/otp_type.enum';
import { HttpException, HttpStatus, InternalServerErrorException } from '@nestjs/common';
import * as bcrypt from 'bcryptjs';
import { Request } from 'express';

export class CoreUtils {
  /**
   * Generic response function for sending responses to the client.
   * @param response - The response object from the controller.
   * @param status - The HTTP status code to send.
   * @param message - The message to send in the response.
   * @param data - The data to send in the response.
   */

  static emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  static genericResponse(response: any, status: HttpStatus, message: string, data: any = null) {
    const responseData: any = {
      statusCode: status,
      message: message,
    };

    if (data) {
      responseData.data = data;
    }

    return response.status(status).json(responseData);
  }

  /**
   * Hash a password using bcrypt.
   * @param password - The password to hash.
   * @param hashedPassword - The hashed password to compare.
   * @returns string - The hashed password.
   */
  static passwordMatch(password: string, hashedPassword: string): boolean {
    return bcrypt.compareSync(password, hashedPassword);
  }

  /**
   * Hash a password using bcrypt.
   * @param password - The password to hash.
   * @returns string - The hashed password.
   */
  static hashPassword(password: string): string {
    return bcrypt.hashSync(password, 10);
  }

  /**
   * Generates a unique token with uppercase letters and numbers.
   * @param length - The desired length of the token. Default is 9.
   * @returns A randomly generated token.
   */
  static generateOtp(length = 4): string {
    const characters = '0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters[randomIndex];
    }

    return result;
  }

  /**
   * Generates a token with uppercase letters and numbers.
   * @param seconds - The number of seconds until the token expires.
   * @returns A randomly generated token.
   */
  static generateExpiryDate(seconds: number): Date {
    const now = new Date();
    now.setSeconds(now.getSeconds() + seconds);
    return now;
  }

  /**
   * Generates a complete link for email.
   * @param baseURL - The base URL to prefix the token. Default is an empty string.
   * @param screenPath
   * @returns A complete link to send via email.
   */
  static generateLink(baseURL = '', screenPath: string): string {
    // const token = this.generateToken(length);
    return `${baseURL}${baseURL && baseURL.endsWith('/') ? '' : '/'}${screenPath}`;
  }

  static generateRandomPassword(length: number) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const charactersLength = characters.length;
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charactersLength);
      result += characters[randomIndex];
    }

    return result;
  }

  /**
   * Calculates the start of the day for a given date.
   * @param date - The input date.
   * @returns The start of the day (00:00:00) for the given date.
   */
  static getStartOfDay(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  }

  /**
   * Gets the start of the previous day based on the given date.
   * @param date - The input date.
   * @returns The start of the previous day.
   */
  static getStartOfPreviousDay(date: Date): Date {
    const startOfDay = CoreUtils.getStartOfDay(date);
    startOfDay.setDate(startOfDay.getDate() - 1);
    return startOfDay;
  }

  /**
   * Calculates the percentage change between two values.
   * @param currentValue - The current value.
   * @param previousValue - The previous value.
   * @returns The percentage change.
   */
  static calculatePercentageChange(currentValue: number, previousValue: number): number {
    return previousValue === 0 ? 0 : ((currentValue - previousValue) / previousValue) * 100;
  }

  /**
   * Validate if all parameters are strings.
   * @param {object} params - Object containing parameters to validate.
   */
  static validateStringParams(params: object) {
    for (const [key, value] of Object.entries(params)) {
      if (typeof value !== 'string') throw new Error(`${key} must be a string`);
    }
  }

  /**
   * Throws an error with the appropriate status code.
   * @param error
   */
  static throwError(error: any) {
    throw error.status == undefined
      ? new InternalServerErrorException(error?.message) // Server error
      : new HttpException(error?.message, error.status); // Client error
  }

  /**
   * Check if a token, secret, or key is expired.
   * @param minutes
   * @param createdAt
   */
  static isExpired(minutes: number, createdAt: Date): boolean {
    // Convert minutes to milliseconds
    const expirationTime = createdAt.getTime() + minutes * 60 * 1000;
    // Get current time
    const now = Date.now();
    // Check if expired
    return now > expirationTime;
  }

  /**
   * Standardized Error Handling for the application.
   * @param action
   */
  static async handleRequest(action: () => Promise<{ message: string; data?: any }>) {
    try {
      return await action();
    } catch (error) {
      CoreUtils.throwError(error);
    }
  }

  /**
   * Generates a unique tracking number.
   * @param prefix Optional prefix for the tracking number (e.g., "TRK").
   * @param length Length of the unique portion (excluding the prefix).
   * @returns A tracking number string.
   */
  static generateTrackingNumber(prefix: string = 'TRK', length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomChar = chars.charAt(Math.floor(Math.random() * chars.length));
      result += randomChar;
    }

    // Optionally include a timestamp for extra uniqueness
    const timestamp = Date.now().toString().slice(-5); // Last 5 digits of the timestamp

    return `${prefix}-${result}${timestamp}`;
  }

  static getOtpCacheKey(userId: number, type: OtpType): string {
    return `otp-${type}-${userId}`;
  }

  static getCurrentRoute(req: Request): string {
    return `${req.protocol}://${req.get('host')}${
      req.originalUrl.split('?')[0]
    }`;
  }

}
